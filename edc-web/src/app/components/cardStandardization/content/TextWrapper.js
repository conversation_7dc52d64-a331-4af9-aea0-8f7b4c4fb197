import React, { useContext } from 'react';
import { string, bool } from 'prop-types';
import configWrapper from '../hoc/configWrapper';
import { safeRender } from 'edc-web-sdk/requests/renderingOptions';
import CardContext from '../context/CardContext';
import classnames from 'classnames';
import { useWindowSize } from 'centralized-design-system/src/Utils/hooks';
import { ANCHOR_TAG_WITH_ATTRIBUTES_REGEX } from '../../../../app/constants/regexConstants';
import { modifyAnchorTagsForNewTab } from '../utils/modifyAnchorTagsForNewTab';
import Tooltip from 'centralized-design-system/src/Tooltip';

const TextWrapper = ({ message, isStandaloneLayout, listCard = false, openInNewWindow, fullTitle }) => {
  const CardContextData = useContext(CardContext);
  let updatedMessage = message;
  const windowSizeObject = useWindowSize();
  const screenSize = windowSizeObject.width;
  const cardHeadingId = CardContextData?.card?.id + '-title';
  if (
    (listCard && message?.length > 65) ||
    (isStandaloneLayout && CardContextData.isCurateTab && screenSize < 550)
  ) {
    updatedMessage = message.substring(0, 62);
    updatedMessage = `${updatedMessage}...`;
  }

  // Add or remove target="_blank" based on config
  updatedMessage = updatedMessage?.replace(
    ANCHOR_TAG_WITH_ATTRIBUTES_REGEX,
    modifyAnchorTagsForNewTab(openInNewWindow)
  );

  const renderTooltipTitle = () => {
    return (
      fullTitle ? (
        <Tooltip
          message={fullTitle}
          isHtmlIncluded={true}
          pos="top"
          id="card-title-tooltip"
        >
          <span className="font-weight-400 font-size-l" id={cardHeadingId}>
            <span
              className={classnames({ 'list-card-title-text': listCard })}
              dangerouslySetInnerHTML={{
                __html: safeRender(updatedMessage)
              }}
            />
          </span>
        </Tooltip>
      ) : (
        <span className="font-weight-400 font-size-l" id={cardHeadingId}>
          <span
            className={classnames({ 'list-card-title-text': listCard })}
            dangerouslySetInnerHTML={{
              __html: safeRender(updatedMessage)
            }}
          />
        </span>
      )
    );
  };

  return isStandaloneLayout ? (
    <h1
      className={classnames({
        'card-title-header': isStandaloneLayout,
        'list-card-title-text': listCard
      })}
      dangerouslySetInnerHTML={{
        __html: safeRender(updatedMessage)
      }}
      id={cardHeadingId}
    />
  ) : (
    renderTooltipTitle()
  );
};

TextWrapper.propTypes = {
  message: string,
  isStandaloneLayout: bool,
  listCard: bool,
  openInNewWindow: bool
};

export default configWrapper(TextWrapper);
