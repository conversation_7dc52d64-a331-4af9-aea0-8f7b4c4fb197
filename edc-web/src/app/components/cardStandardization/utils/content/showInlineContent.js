import {
  isLmsWorkFlowTrue,
  shouldShowLikeArticleView,
  hasLMSWorkflow,
  hasPreActivity,
  isLmsPaidContent,
  isPaymentCompleted,
  isWorkflowPresentForLmsPaidContent
} from '../../../../utils/utils';
import { FILE_MEDIA_TYPE, IMAGE_MEDIA_TYPE } from '../../common/constants';

const showInlineContent = card => {
  const { launchInline, contentInlinePlayConfiguration = {}, resource, transcriptData = {} } = card;
  const isEmbedHtmlPresent = !!resource?.embedHtml;
  const isLaunchInlinePresent = 'launchInline' in card;
  const { mediaType } = contentInlinePlayConfiguration;
  const hasTranscriptData = Object.keys(transcriptData).length !== 0;

  const requiresPayment = isLmsPaidContent(card);
  const hasCompletedPayment = isPaymentCompleted(transcriptData?.contentStatus);
  const canPlayInline = !hasPreActivity(card);

  const canPlayCourseInline = isWorkflowPresentForLmsPaidContent(card)
    ? false
    : requiresPayment
    ? hasCompletedPayment && canPlayInline
    : canPlayInline;

  const launchInlineValue =
    (hasLMSWorkflow(card) && isLmsWorkFlowTrue(card) && launchInline) || requiresPayment
      ? hasTranscriptData === true
        ? !shouldShowLikeArticleView(card, transcriptData)
        : canPlayCourseInline //launch content inline for course cards with post activity
      : launchInline;

  if (isLaunchInlinePresent && launchInlineValue === false) {
    return false;
  }

  if (launchInlineValue && !isEmbedHtmlPresent) {
    // We  currenty only support mediatype for pdf, video, FILE_MEDIA_TYPE and IMAGE_MEDIA_TYPE
    // for rest it will show as normal card
    if (
      FILE_MEDIA_TYPE.includes(mediaType) ||
      IMAGE_MEDIA_TYPE.includes(mediaType) ||
      mediaType === 'pdf' ||
      mediaType === 'video'
    ) {
      return true;
    }
    return null;
  }
  return null;
};

export const inlineLinkMediaType = card => {
  const { launchInline, contentInlinePlayConfiguration = {}, resource } = card;
  const { mediaType } = contentInlinePlayConfiguration;
  return launchInline && !resource?.embedHtml && mediaType === 'link';
};

export default showInlineContent;
