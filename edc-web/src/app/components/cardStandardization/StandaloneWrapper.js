import React, { lazy } from 'react';
import { object, bool, func, string } from 'prop-types';
import ErrorBoundaryWrapper from './hoc/ErrorBoundaryWrapper';
import { Translatr } from 'centralized-design-system/src/Translatr';
import LD from '../../../app/containers/LDStore';
import { getTranslationApps } from '@utils/utils';
const Standalone = lazy(() => import('./Standalone'));

const StandaloneWrapper = props => {
  // isActionsDisabled is required to handle if card is in curateSection
  const {
    card,
    isActionsDisabled,
    isStandaloneModal,
    cardUpdated,
    dataCard,
    dismissible,
    showTopicToggleClick,
    openReasonReportModal,
    removeCardFromList,
    deleteSharedCard,
    channel,
    cardSectionName,
    removeCardFromCardContainer,
    isPartOfPathway,
    isCurateTab
  } = props;

  // Get translation apps with subscription translations conditionally included
  const translationApps = getTranslationApps(
    ['web.smartcard.standalone'],
    LD.isSubscriptionFeatureEnabled()
  );
  return (
    <Translatr apps={translationApps}>
      <Standalone
        card={card}
        isActionsDisabled={isActionsDisabled}
        isStandaloneModal={isStandaloneModal}
        cardUpdated={cardUpdated}
        dataCard={dataCard}
        dismissible={dismissible}
        showTopicToggleClick={showTopicToggleClick}
        openReasonReportModal={openReasonReportModal}
        removeCardFromList={removeCardFromList}
        deleteSharedCard={deleteSharedCard}
        channel={channel}
        cardSectionName={cardSectionName}
        removeCardFromCardContainer={removeCardFromCardContainer}
        isPartOfPathway={isPartOfPathway}
        isCurateTab={isCurateTab}
      />
    </Translatr>
  );
};

StandaloneWrapper.propTypes = {
  card: object,
  isActionsDisabled: bool,
  isStandaloneModal: bool,
  cardUpdated: func,
  dataCard: object,
  dismissible: bool,
  showTopicToggleClick: func,
  openReasonReportModal: func,
  removeCardFromList: func,
  deleteSharedCard: func,
  channel: object,
  cardSectionName: string,
  removeCardFromCardContainer: func,
  isPartOfPathway: bool,
  isCurateTab: bool
};

StandaloneWrapper.defaultProps = {
  isActionsDisabled: false
};

export default ErrorBoundaryWrapper(StandaloneWrapper);
