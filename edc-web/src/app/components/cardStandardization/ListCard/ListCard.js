import React, { useState, useEffect } from 'react';
import { object, bool, array, func, string, number } from 'prop-types';
import { useSelector } from 'react-redux';
import cn from 'classnames';
import { translatr } from 'centralized-design-system/src/Translatr';
import Tooltip from 'centralized-design-system/src/Tooltip';
import Dropdown from 'centralized-design-system/src/Dropdown';
import DeleteCardConfirmationModal from '@components/modals/DeleteCardConfirmationModal';
import RemoveCardConfirmationModal from '@components/modals/RemoveCardConfirmationModal';
import AuthorDetails from '../header/author/AuthorDetails';
import ThumbnailWrapper from '../content/ThumbnailWrapper';
import Title from '../content/Title';
import LeapModal from '@components/modals/LeapModal';
import { Permissions } from '../../../../app/utils/checkPermissions';
import getStandardizedTitle from '../utils/content/getStandardizedTitle';
import getInitialStateForLockedCards from '../utils/getInitialStateForLockedCards';
import { useWindowSize } from 'centralized-design-system/src/Utils/hooks';
import getLeapedCardsInPathway from '../utils/getLeapedCardsInPathway';
import { stripHtmlTagsIfNotOldTextCard } from '@utils/utils';
import { getContentTypeLabel } from '@utils/getContentTypeLabel';
import { CARD_STATE } from '../common/constants';
import { selectCardOptionalStatus } from '@pages/PathwayAndJourney/common/selectors';

import './ListCard.scss';

const ListCard = ({
  card,
  type,
  removeCardFromList,
  index,
  configHeader,
  configContent,
  filestackUrlExpire,
  currentUserId,
  cardsList,
  leapData,
  addToLeap,
  pathway,
  cardType,
  pathwayDetails,
  journeyDetails,
  journeyState,
  lockCard,
  lockedCards,
  isOwner,
  pathwayEditor,
  cardUrl,
  updateCard,
  hideEdit,
  resetLeap,
  setResetLeap,
  isJourney,
  disableEdit,
  sectionId
}) => {
  const [isCardLocked, setIsCardLocked] = useState(
    getInitialStateForLockedCards({
      isJourney,
      lockedCards,
      card
    }) || false
  );

  const [isLeapActive, setIsLeapActive] = useState(
    leapData?.some(leapCard => leapCard.cardId === card.id) || false
  );

  const [lockTooltip, setLockTooltip] = useState('');
  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const [openRemoveModal, setOpenRemoveModal] = useState(false);
  const [openLeapModal, setOpenLeapModal] = useState(false);

  const isOptional = useSelector(state =>
    selectCardOptionalStatus({ state, cardId: card.id, sectionId })
  );

  useEffect(() => {
    let isCurrentCardLocked = false;

    isCurrentCardLocked = isJourney
      ? lockedCards.filter(lockCardObj => lockCardObj.id === card.id)?.[0]?.isLocked === 'true'
      : !!lockedCards && lockedCards[card.id] === 'true';

    if (isCurrentCardLocked) {
      setIsCardLocked(true);
    }
  }, []);

  useEffect(() => {
    isCardLocked
      ? setLockTooltip(translatr('web.common.main', 'LockSmartCard'))
      : setLockTooltip(translatr('web.common.main', 'UnlockSmartCard'));
  }, [isCardLocked]);

  const {
    fileConfig,
    resourceWrapper,
    thumbnailDetails,
    showPlayIcon,
    scormErrorMessage,
    scheduleStreamStartTime,
    thumbnail,
    showRTE
  } = configContent;
  const typeLowerCase = type.toLowerCase();
  const isStandaloneLayout = typeLowerCase === 'standalone';
  const hideThumbnailForFileCardInStandaloneLayout =
    isStandaloneLayout && fileConfig?.configData && fileConfig?.mimeType !== 'audio';
  const showThumbnailDetails =
    !(type === 'featured' && !resourceWrapper) && !hideThumbnailForFileCardInStandaloneLayout;

  let titleToDisplay = getStandardizedTitle(card);
  titleToDisplay = stripHtmlTagsIfNotOldTextCard(card, titleToDisplay);

  const titleConfig = titleToDisplay
    ? {
        message: titleToDisplay,
        withoutThumbnails: !thumbnail,
        showRTE: showRTE && !isStandaloneLayout,
        fullTitle: titleToDisplay,
      }
    : null;

  const deleteClickHandler = (e = null) => {
    e?.stopPropagation();
    setOpenDeleteModal(!openDeleteModal);
  };
  const viewClickHandler = e => {
    e.stopPropagation();
    window.open(cardUrl, '_blank', 'noopener,noreferrer');
  };
  const removeClickHandler = (e = null) => {
    e?.stopPropagation();
    setOpenRemoveModal(!openRemoveModal);
  };

  const leapClickHandler = (e = null) => {
    e?.stopPropagation();
    setOpenLeapModal(!openLeapModal);
  };

  const lockClickHandler = (e = null, cardData) => {
    e?.stopPropagation();
    setIsCardLocked(!isCardLocked);
    lockCard(cardData);
  };
  const checkLeapStatus = element => card.id == element?.cardId;

  const isShowLeap = (() => {
    const leapedPathways = getLeapedCardsInPathway(pathway);

    const createLeap =
      cardType === 'quiz' &&
      Permissions['enabled'] !== undefined &&
      Permissions.has('CREATE_LEAP') &&
      !leapedPathways?.filter(checkLeapStatus)?.length &&
      !(pathwayDetails || journeyDetails) &&
      'create';

    const updateLeapCommon =
      cardType === 'quiz' &&
      Permissions['enabled'] !== undefined &&
      Permissions.has('UPDATE_LEAP') &&
      leapedPathways?.filter(checkLeapStatus)?.length &&
      pathway?.state === CARD_STATE.DRAFT &&
      card.state === CARD_STATE.PUBLISHED &&
      'update';

    const updateLeap = journeyState
      ? journeyState === CARD_STATE.DRAFT && updateLeapCommon
      : updateLeapCommon;

    return createLeap || updateLeap;
  })();

  const openEditPopup = e => {
    e.stopPropagation();
    window.dispatchEvent(
      new CustomEvent('openSmartBiteModal', {
        detail: { open: true, card, pathwayJourneyCallback: updateCard }
      })
    );
  };

  const dropDownbtnClass = cn('pointer ed-text-color text-left width-100');

  const dropDownChildren = (
    <ul>
      <li>
        <button className={dropDownbtnClass} onClick={viewClickHandler}>
          <i className="icon-eye" />
          {translatr('web.common.main', 'ViewCardtype', {
            cardType: translatr('web.common.main', card?.cardType) || card?.cardType || ''
          })}
        </button>
      </li>
      {!hideEdit && (
        <li>
          <button className={dropDownbtnClass} onClick={openEditPopup}>
            <i className="icon-edit-light" />
            {translatr('web.common.main', 'EditCardtype', {
              cardType: translatr('web.common.main', card?.cardType) || card?.cardType || ''
            })}
          </button>
        </li>
      )}
      <li>
        <button className={dropDownbtnClass} onClick={removeClickHandler}>
          <i className="icon-minus-circle" />
          {translatr('web.common.main', 'RemoveFromPathway')}
        </button>
      </li>
      {card.hidden && isOwner && (
        <li>
          <button className={dropDownbtnClass} onClick={deleteClickHandler}>
            <i className="icon-trash" />
            {translatr('web.common.main', 'DeleteCardtype', {
              cardType: translatr('web.common.main', card?.cardType) || card?.cardType || ''
            })}
          </button>
        </li>
      )}
    </ul>
  );

  const moreIcon = (
    <Tooltip message={translatr('web.common.main', 'More')} margin={0} isTranslated>
      <i className="icon-ellipsis-h" />
    </Tooltip>
  );

  const windowSizeObject = useWindowSize();
  const screenWidth = windowSizeObject.width;

  function listCardTitle() {
    return (
      <div className="justflex align-items-center flex-2">
        <Title
          listCard={true}
          configData={titleConfig}
          isStandaloneLayout={isStandaloneLayout}
          showFilePreview={fileConfig?.mimeType === 'application'}
        />
      </div>
    );
  }
  const isCompletedCard =
    cardType === 'quiz' && hideEdit === "Can't edit completed poll/quiz" ? true : false;

  return (
    <>
      <div className="List-Card justflex">
        <div className="float-left justflex flex-1 align-items-center list-card-number">
          <Tooltip
            message={translatr('web.common.main', 'MoveSmartcard')}
            pos={'right'}
            isTranslated
          >
            <i className="icon-grip pointer" />
          </Tooltip>
          <Tooltip message={translatr('web.common.main', 'SmartcardPosition')} isTranslated>
            <div className="card-index item-box justflex align-items-center justify-center">
              {index}
            </div>
          </Tooltip>
          <div className="thumbnail-container">
            {showThumbnailDetails && (
              <>
                <ThumbnailWrapper
                  {...thumbnailDetails}
                  type={typeLowerCase}
                  cardId={card.id}
                  filestackUrlExpire={filestackUrlExpire}
                  currentUserId={currentUserId}
                  showPlayIcon={showPlayIcon}
                  scormErrorMessage={scormErrorMessage}
                  cardLanguageErrorMessage={configContent.cardLanguageErrorMessage}
                  isScormCard={thumbnailDetails?.isScormCard}
                  showOverlay={['pack', 'journey'].includes(cardType) && !isStandaloneLayout}
                  {...scheduleStreamStartTime}
                />
              </>
            )}
          </div>
        </div>
        {screenWidth > 400 && listCardTitle()}
        <div className="justflex flex-2 align-items-center creator-value-text">
          <AuthorDetails configData={configHeader.authorDetails} />
        </div>
        <p className="justflex align-items-center flex-1 type-value-text">
          {getContentTypeLabel(card?.readableCardType) || card?.readableCardType}
        </p>
        <p className="justflex align-items-center flex-1 level-value-text">{card.skillLevel}</p>
        <div className="flex-end align-items-center flex-1">
          {!disableEdit && isShowLeap && (isOwner || pathwayEditor) && !isCompletedCard && (
            <Tooltip
              message={
                isShowLeap === 'create'
                  ? translatr('web.common.main', 'Leap')
                  : translatr('web.common.main', 'UpdateLeap')
              }
            >
              <button
                className={cn(
                  'leap-icon-btn item-box justflex align-items-center justify-center pointer',
                  {
                    active: isLeapActive && !isOptional,
                    'not-allowed': isOptional
                  }
                )}
                onClick={leapClickHandler}
                disabled={isOptional}
              >
                <i className="icon-repeat ed-text-color" />
              </button>
            </Tooltip>
          )}
          {!disableEdit && !isCompletedCard && (
            <Tooltip message={lockTooltip} isTranslated>
              <button
                className={cn(
                  'lock item-box justflex align-items-center justify-center pointer ed-text-color',
                  {
                    'not-allowed': isOptional
                  }
                )}
                onClick={e => lockClickHandler(e, card)}
                onMouseEnter={() => !isOptional && setIsCardLocked(!isCardLocked)}
                onMouseLeave={() => !isOptional && setIsCardLocked(!isCardLocked)}
                disabled={isOptional}
              >
                {isCardLocked || card.isLocked ? (
                  <i className="icon-lock" />
                ) : (
                  <i className="icon-unlock" />
                )}
              </button>
            </Tooltip>
          )}
          <div className="more justflex align-items-center justify-center pointer">
            <Dropdown icon={moreIcon} children={dropDownChildren} />
          </div>
        </div>
      </div>
      {screenWidth <= 400 && listCardTitle()}

      {openDeleteModal && (
        <DeleteCardConfirmationModal
          card={card}
          closeHandler={deleteClickHandler}
          removeCardFromList={removeCardFromList}
        />
      )}
      {openRemoveModal && (
        <RemoveCardConfirmationModal
          card={card}
          type={type}
          closeHandler={removeClickHandler}
          removeCardFromList={removeCardFromList}
          headerText={translatr('web.common.main', 'Pathway')}
        />
      )}

      <LeapModal
        open={openLeapModal}
        setIsLeapActive={setIsLeapActive}
        card={card}
        type={type}
        closeHandler={leapClickHandler}
        cardsList={cardsList}
        addToLeap={addToLeap}
        pathway={pathway}
        resetLeap={resetLeap}
        setResetLeap={setResetLeap}
        leapData={leapData}
      />
    </>
  );
};

ListCard.propTypes = {
  card: object,
  type: string,
  removeCardFromList: func,
  index: number,
  configHeader: object,
  configContent: object,
  filestackUrlExpire: number,
  currentUserId: string,
  cardsList: array,
  addToLeap: func,
  pathway: object,
  cardType: string,
  journeyState: string,
  lockCard: func,
  isOwner: bool,
  pathwayEditor: bool,
  cardUrl: string,
  updateCard: func,
  hideEdit: bool,
  resetLeap: bool,
  setResetLeap: func,
  lockedCards: array,
  leapData: array,
  isJourney: bool,
  disableEdit: bool,
  pathwayDetails: object,
  journeyDetails: object,
  sectionId: string
};

export default ListCard;
