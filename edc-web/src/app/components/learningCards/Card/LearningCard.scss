@import 'centralized-design-system/src/Styles/variables';
@import '~styles/base';

.learning-card {
  & > div {
    text-align: start;
    width: 100%;
    padding: 2px;
    border: 2px solid transparent;
    &[aria-disabled='true'] {
      cursor: default;
      & .learning-card-content-details-info-title:hover {
        text-decoration: none !important;
      }
    }
    &[aria-disabled='false']:focus {
      cursor: pointer;
      outline: var(--ed-primary-darken-1) solid 2px;
      border-radius: 4px;
    }
    &:hover {
      border-radius: 4px;
    }
  }
  &-container {
    display: flex;
    flex-direction: row;
    gap: 16px;
    & .learning-card-content-details-info-title:hover {
      text-decoration: underline;
    }
  }
  &-thumbnail {
    min-width: 64px;
    width: 64px;
    height: 64px;
    border-radius: 4px;
    object-fit: cover;
  }
  &-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    &-details {
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      &-info {
        color: $dark-gray !important;
        &-title {
          display: flex;
          align-items: center;
          flex-direction: row;
          flex-wrap: wrap;
          max-width: 540px;
          gap: 0 8px;
          & .learning-card-tooltip > span {
            line-height: 24px;
            font-size: rem-calc(15px);
            font-weight: 700;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            @supports (-webkit-line-clamp: 2) {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: initial;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
            }
          }
          & > .ed-tag-container {
            font-size: rem-calc(13);
            line-height: 24px;
          }
        }
        &-author {
          height: 24px;
          line-height: 21px;
          font-size: rem-calc(14);
          overflow: hidden;
        }
        &-data {
          line-height: 21px;
          font-size: rem-calc(14);
          & .bold {
            font-weight: 600;
          }
          & .languages {
            display: inline-flex;
            align-items: center;
          }
          & > span:not(:last-child):after {
            content: '';
            margin: 0 8px;
            border: 1px solid var(--ed-gray-3);
          }
          & .languages-tag {
            border: none;
            padding: 2px 8px;
            margin: 0 0 0 8px;
            height: 17px;
            background-color: var(--ed-gray-1);
            font-size: rem-calc(10);
          }
          & .due-date {
            &-red {
              color: var(--ed-negative-2);
            }
            &-yellow {
              color: var(--ed-warning-1);
            }
          }
        }
        &-status {
          line-height: 21px;
          color: var(--ed-gray-5);
          font-size: rem-calc(14);
        }
      }
      &-actions {
        display: flex;
        flex-direction: row;
        & .ed-dropdown {
          margin: 0 !important;
          & i {
            height: fit-content;
            font-size: var(--ed-icon-size-md);
          }
          & .dropdown-btn {
            justify-content: center;
          }
          .tab-drpdwn {
            .dropdown-content {
              & > ul > li {
                padding: 0;
                .ed-tooltip {
                  width: 100%;
                  button {
                    height: fit-content;
                    width: 100%;
                    line-height: var(--ed-line-height-base);
                    outline-color: inherit;
                    &[aria-disabled='true'] {
                      cursor: not-allowed;
                      color: var(--ed-gray-3);
                    }
                  }
                }
              }
            }
          }
          .dropdown-content {
            min-width: 139px;
            padding: 8px;
            & > ul > li {
              padding: 5px 8px 5px 8px;
              &:not(:last-child) {
                margin-bottom: 4px;
              }
              &:hover {
                border-radius: 4px;
                background: var(--ed-button-primary-ghost-active-bg-color);
              }
              &[aria-disabled='true'] {
                cursor: not-allowed;
                color: var(--ed-gray-3);
              }
            }
          }
        }
        & button {
          width: 32px;
          height: 32px;
          cursor: pointer;
          &:focus {
            outline: var(--ed-primary-darken-1) solid 2px;
            border-radius: 4px;
          }
        }
        & .icon-ellipsis-h {
          width: 32px;
          max-width: 32px;
          height: 32px;
          color: $dark-gray;
          font-size: 32px;
          font-weight: bold;
        }
      }
    }
    &-progress {
      padding-top: 12px;
      width: 100%;
      &-container {
        height: 4px;
        background: rgba(0, 0, 0, 0.08);
        border-radius: 4px;
      }
      &-bar {
        height: 4px;
        background: var(--ed-primary-base);
        border-radius: 4px;
      }
      &-value {
        padding-top: 4px;
        font-size: rem-calc(12);
        color: $dark-gray;
        line-height: 18px;
      }
    }
    &-assignors {
      display: flex;
      &-showhide {
        text-align: left;
      }
      &-item {
        flex: 1;
        span {
          display: block;
          line-height: 21px;
        }
        &-label {
          display: block;
          font-size: 12px;
          line-height: 18px;
        }
      }
    }
  }
}

@media only screen and (max-width: 536px) {
  .learning-card-content-details-actions button {
    display: none;
  }
  .learning-card-content-progress {
    padding: 8px 0;
    &-value {
      display: none;
    }
  }
}
