import React, { useEffect, useMemo, useState } from "react";
import { useNavigate } from "react-router-dom";
import cn from "classnames";
import moment from "moment";
import { getCardImage } from "@utils/smartCardUtilsV2";
import stripHTMLTags from "@utils/stripHTMLTags";
import { getTranslatedContentStatus, isOldTextCard } from "@utils/utils";
import getStaticImgPath from "edc-web-sdk/helpers/getStaticImgPath";
import { safeRender } from 'edc-web-sdk/requests/renderingOptions';
import { tr } from "edc-web-sdk/helpers/translations";
import { getTranslatedSkillLevelLabel } from "centralized-design-system/src/Utils/proficiencyLevels";
import { translatr } from "centralized-design-system/src/Translatr";
import Dropdown from "centralized-design-system/src/Dropdown";
import { DisplayTag, FlagTag } from "centralized-design-system/src/Tags";
import Tooltip from "centralized-design-system/src/Tooltip";
import { useWindowSize } from "centralized-design-system/src/Utils/hooks";
import { langs } from "../../../constants/languages";
import useMarkAsComplete, { OnCompleteChange } from "../hooks/useMarkAsComplete";
import { formatDate } from "../utils";
import { LearningCardData } from "../index";
import LearningCardAssignors, { LearningCardAssignorsProps } from "./LearningCardAssignors";
import "./LearningCard.scss";

const MOBILE_SCREEN_SIZE = 320;

const getCardTitle = (card: LearningCardData) => {
  const title = card.title || card.message;
  return isOldTextCard(card.type.toLowerCase(), !!card?.title) && !card?.filestack?.length // old text card without image.
    ? title
    : stripHTMLTags(title);
};

const getDisabledReason = (card: LearningCardData) => {
  if (card.state === "archived") {
    return translatr("web.common.main", "Archived");
  }
  if (card.state === "deleted") {
    return translatr("web.common.main", "Deleted2");
  }
  return null;
}

const getDueDateClassPostfix = (date: string) => {
  const currentDate = moment.parseZone().local();
  const dueDate = moment.parseZone(date).local();
  const daysToDueDate = dueDate.diff(currentDate, "days");
  if (daysToDueDate <= 2) {
    return "red";
  }
  if (daysToDueDate <= 13) {
    return "yellow";
  }
  return "black";
};

export const getFullLanguage = (langCode: string) =>
  Object
    .keys(langs)
    // @ts-ignore
    .find(key => langs[key] === langCode);

interface MenuProps {
  card: LearningCardProps["card"],
  menu: LearningCardProps["menu"],
  isOpen: boolean,
  setMenuOpen: (v: boolean) => void,
}
const Menu = ({ card, menu, isOpen, setMenuOpen }: MenuProps) => {
  const markAsComplete = menu?.markAsComplete ? useMarkAsComplete(card) : null;

  const isMenuAvailable = menu?.list?.length || markAsComplete?.actionVisible;
  if (!isMenuAvailable) {
    return null;
  }

  const renderEntry = (entry: LearningCardMenuEntry, idx: number) => {
    const disabled = entry.disabled?.(card);
    const tooltipHidden = typeof disabled !== "string";
    return (
      <li key={`${card.id}-menu-${idx}`}>
        <Tooltip
          id={`tooltip-${card.id}-${idx}`}
          message={tooltipHidden ? '' : disabled}
          hide={tooltipHidden}
          isTranslated
        >
          <button
            onClick={(e: any) => {
              e.stopPropagation();
              if (disabled) {
                return;
              }
              entry.action(card);
              if (entry.async) {
                //so that switching into loading or disabled state can be seen by user
                setTimeout(() => {
                  setMenuOpen(false);
                }, 250);
              } else {
                setMenuOpen(false);
              }
            }}
            aria-disabled={!!disabled}
            aria-describedby={`${card.id}-${idx}__tooltip-to-remove`}
          >
            {entry.label instanceof Function ? entry.label(card) : entry.label}
          </button>
          {!tooltipHidden && (
            <div
              id={`${card.id}-${idx}__tooltip-to-remove`}
              className="sr-only"
              dangerouslySetInnerHTML={{ __html: String(disabled) }}
            />
          )}
        </Tooltip>
      </li>
    );
  };
  return (
    <Dropdown
      icon={<i className="icon-ellipsis-h" id={`learning-card-${card.id}-menu`} />}
      wrapperClass="tab-drpdwn"
      openDropdown={isOpen}
      setOpenDropdown={() => setMenuOpen(true)}
      ariaLabel={translatr('web.common.main', 'MoreOpportunityActions', {
        opportunity: card.title
      })}
      ariaHasPopup="menu"
    >
      <ul>
        {menu.markAsComplete && markAsComplete?.actionVisible &&
          renderEntry({
            label: markAsComplete.isCardCompleted ?
              translatr("web.common.main", "Uncomplete") : translatr("web.common.main", "MarkAsComplete2"),
            action: () => markAsComplete.isCardCompleted ?
              markAsComplete.uncomplete(menu.markAsComplete) : markAsComplete.complete(menu.markAsComplete),
            disabled: () => markAsComplete.inProgress || (markAsComplete.actionUnavailable && markAsComplete.actionUnavailableMessage),
            async: true,
          }, 0)
        }
        {menu.list
          ?.filter(entry => !entry.hidden?.(card))
          .map(renderEntry)
        }
      </ul>
    </Dropdown>
  );
};

type LearningCardMenuEntry = {
  label: string | ((card: LearningCardData) => string);
  action: (card: LearningCardData) => void;
  hidden?: (card: LearningCardData) => boolean;
  disabled?: (card: LearningCardData) => boolean | string;
  async?: boolean;
};

interface LearningCardProps {
  card: LearningCardData;
  menu?: {
    markAsComplete?: OnCompleteChange; //if provided "Mark as complete" menu option will be visible in "..." menu
    list?: Array<LearningCardMenuEntry>;
  };
  onBookmarkClick?: (card: LearningCardData) => void;
  onExpandClick?: LearningCardAssignorsProps["onExpandClick"];
  assignors?: LearningCardAssignorsProps["assignors"];
  assignorsFields?: LearningCardAssignorsProps["assignorsFields"];
  headingLevel?: "h3" | "h4" | "h5";
  disableNavigation?: boolean;
}
const LearningCard = ({
  card,
  menu,
  onBookmarkClick,
  onExpandClick = null,
  assignorsFields = null,
  assignors = null,
  headingLevel = "h3",
  disableNavigation = false,
}: LearningCardProps) => {
  const title = getCardTitle(card);
  const cardDisabledReason = getDisabledReason(card);
  const isCardDisabled = !!cardDisabledReason;
  const defaultThumbnail = getStaticImgPath("/i/images/thumbnail-card-placeholder.png");
  const defaultLanguage = tr(getFullLanguage(card.defaultLanguage));
  const languages = useMemo(() => {
    if (!card.languages) {
      return null;
    }
    return card.languages
      .filter((l: any) => l != card.defaultLanguage)
      .map((l: any) => tr(getFullLanguage(l)) || l);
  }, [card.languages]);

  const navigate = useNavigate();
  const { width: screenWidth } = useWindowSize();

  const [menuOpen, setMenuOpen] = useState(false);
  const [thumbnailData, setThumbnailData] = useState<{ thumbnail: string, altText: string }>({
    thumbnail: defaultThumbnail,
    altText: "",
  });
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [thumbnailLoadingError, setThumbnailLoadingError] = useState(false);

  useEffect(() => {
    const { img: thumbnail, altText } = getCardImage(card) as { img: string, altText: string};
    if (thumbnail) {
      setThumbnailData({
        thumbnail,
        altText,
      });
    }
  }, []);

  const handleCardOnClick = (e: React.MouseEvent<HTMLSpanElement> | React.KeyboardEvent<HTMLSpanElement>) => {
    if (isCardDisabled) {
      return;
    }

    //e.stopPropagation() cannot be used on Dropdown component
    // because it will not trigger close event on other Dropdowns when used in a list
    //@ts-ignore
    const eventFiredFromCard = e.target.id !== `learning-card-${card.id}-menu`;
    if (eventFiredFromCard && card.navigationUrl) {
      navigate(card.navigationUrl);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if (e.key === "Enter" || e.key === " ") {
      e.preventDefault();
      handleCardOnClick(e);
    }
  };

  const handleBookmarkClick = (evt: React.MouseEvent<HTMLButtonElement>, card: LearningCardData) => {
    evt.stopPropagation();
    onBookmarkClick(card);
  };

  const HeadingTag = headingLevel as keyof JSX.IntrinsicElements;

  return (
    <div className="learning-card">
      <div
        aria-disabled={isCardDisabled}
        title={cardDisabledReason}
      >
        <div className="learning-card-container">
          <img
            className="learning-card-thumbnail"
            src={thumbnailData.thumbnail}
            onError={({ currentTarget }) => {
              if (thumbnailLoadingError) {
                return;
              }
              setThumbnailLoadingError(true);
              currentTarget.onerror = null;
              currentTarget.src = defaultThumbnail;
            }}
            alt={thumbnailData.altText || ""}
          />
          <div className="learning-card-content">
            <div className="learning-card-content-details">
              <div className="learning-card-content-details-info">
                <HeadingTag className="learning-card-content-details-info-title">
                  <Tooltip
                    message={title}
                    isHtmlIncluded={true}
                    pos="top"
                    customClass="learning-card-tooltip"
                  >
                  <span
                    dangerouslySetInnerHTML={{__html: safeRender(title)}}
                    onClick={handleCardOnClick}
                    onKeyDown={handleKeyDown}
                    role={disableNavigation || !card.navigationUrl || isCardDisabled ? undefined : "link"}
                    tabIndex={disableNavigation || !card.navigationUrl || isCardDisabled ? undefined : 0}
                  />
                  </Tooltip>
                  {card.assignmentPriority === "mandatory" && (
                    <FlagTag color="warning">{translatr("web.common.main", "Required")}</FlagTag>
                  )}
                  {isCardDisabled && (
                    <FlagTag color="black">{cardDisabledReason}</FlagTag>
                  )}
                </HeadingTag>
                {card?.authorName && (
                  <div className="learning-card-content-details-info-author">
                    {translatr("web.common.main", "By")} {card?.authorName}
                  </div>
                )}
                <div className="learning-card-content-details-info-data">
                  {card.type && screenWidth >= MOBILE_SCREEN_SIZE && (
                    <span className="bold">{card.type}</span>
                  )}
                  {card.skillLevel && screenWidth >= MOBILE_SCREEN_SIZE && (
                    <span>{getTranslatedSkillLevelLabel(card.skillLevel)}</span>
                  )}
                  {card.duration && (
                    <span>{card.duration}</span>
                  )}
                  {card.dueAt && (
                    <span className={`due-date-${getDueDateClassPostfix(card.dueAt)}`}>
                      {`${translatr("web.common.main", "Due")}: ${formatDate(card.dueAt)}`}
                    </span>
                  )}
                  {defaultLanguage && screenWidth >= MOBILE_SCREEN_SIZE && (
                    <span className="languages">
                      <span>{defaultLanguage}</span>
                      {languages?.length > 1 && (
                        <Tooltip
                          pos="top"
                          message={languages.join(", ")}
                        >
                          <DisplayTag
                            tagName={`+${languages.length}`}
                            displayClass="languages-tag"
                          />
                        </Tooltip>
                      )}
                    </span>
                  )}
                </div>
                {card.externalContentStatus && (
                  <div className="learning-card-content-details-info-status">
                    {`${translatr("web.mylearningplan.main", "Status")}: ${getTranslatedContentStatus(card.externalContentStatus)}`}
                  </div>
                )}
              </div>
              <div className="learning-card-content-details-actions">
                {onBookmarkClick && (
                  <button onClick={(evt: React.MouseEvent<HTMLButtonElement>) => handleBookmarkClick(evt, card)}>
                    <i
                      className={cn(
                        "icon-size no-margin",
                        { "icon-bookmark-fill fill-active": card.isBookmarked },
                        { "icon-bookmark gray-text": !card.isBookmarked }
                      )}
                    />
                  </button>
                )}
                {!isCardDisabled && (
                  <Menu
                    card={card}
                    menu={menu}
                    isOpen={menuOpen}
                    setMenuOpen={setMenuOpen}
                  />
                )}
              </div>
            </div>
            {!!card.completedPercentage && (
              <div className="learning-card-content-progress">
                <div className="learning-card-content-progress-container">
                  <div
                    className="learning-card-content-progress-bar"
                    style={{ width: `${card.completedPercentage}%`, }}></div>
                </div>
                <div className="learning-card-content-progress-value">
                  {`${card.completedPercentage}% ${translatr("web.common.main", "Complete")}`}
                </div>
              </div>
            )}
            {onExpandClick &&
              <LearningCardAssignors
                card={card}
                assignorsFields={assignorsFields}
                assignors={assignors}
                onExpandClick={onExpandClick}
              />
            }
          </div>
        </div>
      </div>
    </div>
  );
};

export { LearningCardMenuEntry };
export default LearningCard;
