import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { getTranslatedSkillLevelLabel } from 'centralized-design-system/src/Utils/proficiencyLevels';
import { FILESTACK_DEFAULT_EXPIRY } from 'edc-web-sdk/config/envConstants';
import { fsDecrypt } from 'edc-web-sdk/helpers/filestackSecurity';

import './FeaturedCard.scss';

import { truncateText, getFilestackUrlWithSecurity } from '@utils/utils';
import { getContentTypeLabel } from '@utils/getContentTypeLabel';
import SvgImageResized from 'centralized-design-system/src/SvgImageResized';
import fetchCardType from '@components/cardStandardization/utils/fetchCardType';
import { getCardOrRandomImage, getFeaturedCardTitle } from '@utils/smartCardUtils';
import linkPrefix from '@utils/linkPrefix';
import Tooltip from 'centralized-design-system/src/Tooltip';

// Wrapper component to prevent clicks on tooltip while allowing hover
const HoverOnlyTooltip = ({ children, allowCheckboxToggle = false, ...tooltipProps }) => {
  const handleClick = (e) => {
    // Check if we're inside a checkbox context by looking for a parent label element
    const isInCheckboxContext = e.target.closest('label.checkbox') !== null;

    if (allowCheckboxToggle && isInCheckboxContext) {
      // Allow the click to propagate to the checkbox label for toggling
      // Only prevent the default action if it would navigate (i.e., if there's an <a> tag)
      const linkElement = e.target.closest('a[href]');
      if (linkElement) {
        e.preventDefault();
        // Don't stop propagation - let it bubble up to the checkbox label
      }
      return;
    }

    // For non-checkbox contexts, prevent all click behavior
    e.preventDefault();
    e.stopPropagation();
    e.stopImmediatePropagation();
    return false;
  };

  const handleMouseDown = (e) => {
    // Check if we're inside a checkbox context
    const isInCheckboxContext = e.target.closest('label.checkbox') !== null;

    if (allowCheckboxToggle && isInCheckboxContext) {
      // Allow mousedown to propagate for checkbox functionality
      return;
    }

    // For non-checkbox contexts, prevent mousedown
    e.preventDefault();
    e.stopPropagation();
    e.stopImmediatePropagation();
    return false;
  };

  return (
    <Tooltip {...tooltipProps}>
      <div
        onClick={handleClick}
        onMouseDown={handleMouseDown}
        style={{ cursor: 'default' }}
      >
        {children}
      </div>
    </Tooltip>
  );
};

const FeaturedCard = ({ card, filestackUrlExpireAfterSeconds, currentUserId, allowCheckboxToggle = false }) => {
  const { readableCardType, skillLevel, translatedSkillLevel } = card;
  const title = getFeaturedCardTitle(card);
  const cardType = fetchCardType(card);
  const { img, mimeType } = getCardOrRandomImage(card, cardType, true, currentUserId);
  const expireAfter = filestackUrlExpireAfterSeconds || FILESTACK_DEFAULT_EXPIRY;
  const filestackDetails = card.filestack?.[0] || {};
  const { handle, url } = filestackDetails;
  const cardUrl = `/${linkPrefix(card.cardType)}/${card.slug}`;

  const filestackUrlParams = {
    url,
    expireAfter,
    currentUserId,
    handle
  };
  // Determines whether to show the thumbnail based on the presence of an thumbnail
  const showFile = Boolean(img || (card.filestack.length && mimeType !== 'image/jpeg'));
  /**
   * Selects and returns the appropriate component based on the MIME type of the card.
   *
   * @return {JSX.Element} The selected component to render.
   */
  const selectComponentByMimeType = () => {
    const renderSvgImage = imgUrl => {
      const transform = 'output=format:jpg,page:1/';
      const cache = 'cache=e:31536000/'; //1 year === 31536000 seconds
      return (
        <div className="blurred-thumbnail">
          <div className="default card-std-ie-image-fix">
            <SvgImageResized
              xlinkHref={imgUrl}
              x="-30%"
              y="-30%"
              width="160%"
              height="160%"
              transform={transform}
              cache={cache}
              loading="lazy"
            />
          </div>
        </div>
      );
    };

    switch (mimeType) {
      case 'application':
        const decrypted = fsDecrypt(getFilestackUrlWithSecurity(filestackUrlParams));
        const fileUrl = card.media?.url || decrypted?.filestack_url;
        return renderSvgImage(fileUrl);
      case 'audio':
        return (
          <div className="blurred-thumbnail make-center font-size-30">
            <i className="icon-audio-thumbnail card-metadata-icon"></i>
          </div>
        );
      default:
        return renderSvgImage(img);
    }
  };

  const Component = showFile ? selectComponentByMimeType() : null;

  return (
    <li>
      <a
        href={cardUrl}
        className="featured__container--card justflex"
        target="_blank"
        role="button"
        rel="noopener noreferrer"
      >
        <div className="img-container relative overflow-hidden">{Component}</div>
        <div className="info__container flex-1 supporting-text">
        <HoverOnlyTooltip
          message={title}
          isHtmlIncluded={true}
          pos="top"
          customClass='featured-card-tooltip'
          allowCheckboxToggle={allowCheckboxToggle}
        >
          <div className="info__container--title overflow-hidden">{title}</div>
        </HoverOnlyTooltip>
          <div className="info__container--details">
            <span className="type">
              {getContentTypeLabel(readableCardType) || readableCardType}
            </span>{' '}
            {skillLevel && (
              <>
                {readableCardType && '|'}{' '}
                <span className="level">
                  {truncateText(
                    getTranslatedSkillLevelLabel(translatedSkillLevel || skillLevel),
                    10,
                    '...'
                  )}
                </span>
              </>
            )}
          </div>
        </div>
      </a>
    </li>
  );
};

FeaturedCard.propTypes = {
  card: PropTypes.object,
  filestackUrlExpireAfterSeconds: PropTypes.number,
  currentUserId: PropTypes.string,
  allowCheckboxToggle: PropTypes.bool
};

const mapStateToProps = state => {
  const { filestack_url_expire_after_seconds: filestackUrlExpireAfterSeconds } = state.team.get(
    'config'
  );

  return {
    filestackUrlExpireAfterSeconds,
    currentUserId: state.currentUser.get('id')
  };
};

export default connect(mapStateToProps)(FeaturedCard);
