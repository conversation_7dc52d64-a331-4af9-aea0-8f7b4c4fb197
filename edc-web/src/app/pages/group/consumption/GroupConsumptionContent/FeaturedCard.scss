@import '~styles/_base.scss';
@import '~centralized-design-system/src/Styles/_variables.scss';

.featured-content-list {
  margin: 0px;
}

.featured__container--card {
  padding: var(--ed-spacing-2xs);
  height: rem-calc(76);
  background-color: var(--ed-white);
  border-radius: var(--ed-border-radius-lg);
  padding: var(--ed-spacing-2xs);
  .img-container {
    width: rem-calc(107);
    height: rem-calc(60);
    border-radius: var(--ed-border-radius-lg);
    margin-right: var(--ed-spacing-2xs);
  }
  .info__container {
    &--title {
      word-break: break-word;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      color: var(--ed-text-color-primary);
    }
    .info__container--details {
      color: var(--ed-text-color-supporting);
      .type {
        font-weight: var(--ed-font-weight-semibold);
      }
    }
  }
  &:hover {
    box-shadow: var(--ed-shadow-base);
  }
}

// Tooltip-specific styles to handle pointer events and click prevention
.featured-card-tooltip {
  // Enable pointer events on tooltip to allow hover functionality
  // even when parent has pointer-events: none
  pointer-events: auto !important;

  // Prevent click events on tooltip and its children
  * {
    pointer-events: none !important;
  }

  // Re-enable hover events specifically for the tooltip container
  &:hover {
    pointer-events: auto !important;
  }
}

.learning-lists {
  list-style-type: none;
}
