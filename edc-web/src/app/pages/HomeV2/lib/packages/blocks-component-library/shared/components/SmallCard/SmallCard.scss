.SmallCardWrapper {
  width: 100%;
  max-height: 444px;
  overflow: auto;
  flex: 1;

  -ms-overflow-style: none; /* for Internet Explorer, Edge */
  scrollbar-width: none; /* for Firefox */
  &::-webkit-scrollbar {
    display: none; /* for Chrome, Safari, etc */
  }

  &:has(.ed-ui .ed-carousel-container) {
    overflow: unset;
  }

  // Make all cards the same height
  .ed-carousel-container,
  .ed-carousel-container .ed-carousel-wrapper,
  .ed-carousel-container .ed-carousel-wrapper .ed-carousel {
    height: 100%;
  }

  // Channel cards are inherently smaller, make them grow to fill up similar space as channels
  .group-channel-card-link.group-channel-card {
    display: flex;
    flex-direction: column;

    a {
      flex-grow: 1;
    }
  }
}

.tab-content-sec {
  .SmallCardWrapper {
    max-height: 378px;
  }
}

.SmallCardAction {
  align-self: center;
  button {
    color: var(--ed-state-active-color);
  }
}

.SmallCardBlock {
  border: var(--ed-border-size-sm) solid var(--ed-gray-2);
  border-radius: var(--ed-border-radius-md);
  display: flex;
  flex-direction: column;
  margin-bottom: var(--ed-spacing-2xs);
  padding: var(--ed-spacing-2xs);

  .img-block {
    width: 98px;
    height: 64px;

    img {
      height: 100%;
      width: 100%;
      object-fit: contain;
      border-radius: var(--ed-border-radius-md);
    }
  }

  .err-img-block {
    svg {
      width: 100%;
      height: 100%;
    }
  }

  .SmallCardLink {
    display: flex;
    flex: 1;
    min-width: 0;
  }

  .SmallCardContent {
    display: flex;
    flex: 1 1 100%;
  }

  .SmallCardInfo {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0;

    .small-card-tooltip {
      width: fit-content;
    }
  }

  .SmallCardFooter {
    margin-top: var(--ed-spacing-2xs);

    .progress-container {
      flex-direction: row-reverse;
      align-items: center;

      .progress-value {
        line-height: 1;
      }
    }
  }

  &.ed-ui {
    .small-card__title {
      font-weight: var(--ed-font-weight-bold);
      text-wrap: wrap;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .SmallCardHeadline {
      font-size: var(--ed-font-size-2xs);
      color: var(--ed-gray-6);
    }

    .SmallCardSecondLine {
      color: var(--ed-gray-6);
      font-size: var(--ed-font-size-sm);
    }
  }

  &:hover {
    box-shadow: var(--ed-shadow-hover);
  }
}

.base-card-wrapper {
  .tab-bar.block {
    border-radius: 0 !important;
  }
  .tab-wrapper .tabs .tab .nav-link {
    font-size: var(--ed-font-size-sm);
    font-weight: var(--ed-font-weight-normal);
    text-transform: uppercase;
  }

  .tab-content-sec {
    .SmallCardWrapper {
      max-height: 378px;
    }
  }
}
