import { connect } from 'react-redux';
import { CustomHTML } from '@edc-blocks/blocks-widget-library';
import PropTypes from 'prop-types';

const CustomHTMLComponent = ({ userData = {}, ...rest }) => {
  const langObj = window.__edOrgData.languages || {};
  const languageCode = userData?.profile?.language;
  const languageLabel =
    Object.keys(langObj).find(key => langObj[key] === languageCode) || languageCode;

  const placeholders = {
    user_id: userData.id || '',
    user_external_id: userData.externalId || '',
    user_first_name: userData.firstName || '',
    user_last_name: userData.lastName || '',
    user_job_title: userData?.profile?.jobTitle || '',
    user_job_family_role: userData.jobFamilyLabel || '',
    user_email: userData.email || '',
    user_language: languageLabel || '',
    user_timezone: userData.timezone || ''
  };

  return <CustomHTML {...rest} placeholders={placeholders} />;
};

CustomHTMLComponent.propTypes = {
  currentUser: PropTypes.object,
  userData: PropTypes.object
};

export default connect(({ currentUser }) => ({
  currentUser,
  userData: {
    firstName: currentUser.get('first_name'),
    lastName: currentUser.get('last_name'),
    id: currentUser.get('id'),
    externalId: currentUser.get('externalId'),
    jobFamilyLabel: currentUser.get('jobFamily')?.get('label'),
    profile: currentUser.get('profile')?.toJS(),
    email: currentUser.get('email'),
    timezone: currentUser.get('profile')?.get('timeZone')
  }
}))(CustomHTMLComponent);
