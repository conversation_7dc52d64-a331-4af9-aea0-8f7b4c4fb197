import React, { useState, useRef } from 'react';
import { translatr } from 'centralized-design-system/src/Translatr';
import { useSelector } from 'react-redux';
import Accordion from 'centralized-design-system/src/Accordion';
import { LearningCard, LearningCardData } from '@components/learningCards';
import {
  DevPlanSkill,
  DevPlanAccordionData,
  DevPlanSkillId,
  DevPlanPhaseStatus,
  DevPlanSkillStatus,
  IDevPlanPhase,
  DevPlanContentStatus,
  DevPlanStepStatus
} from '../../constants';
import { countAllContentsForStep, hasNextPhaseInStep, mapResponseToCard } from '../utils';
import { useDevPlan } from '../../DevelopmentPlanProvider';
import DevelopmentPlanNextPhaseUnlock from '../DevelopmentPlanNextPhaseUnlock/DevelopmentPlanNextPhaseUnlock';
import DevelopmentPlanAddContentModal from '../../modals/DevelopmentPlanAddContentModal/DevelopmentPlanAddContentModal';
import DevelopmentPlanAddSkillModal from '../../modals/DevelopmentPlanAddSkillModal/DevelopmentPlanAddSkillModal';
import DevPlanPhaseUnlockedModal from '../../modals/DevPlanPhaseUnlockedModal/DevPlanPhaseUnlockedModal';
import {
  toggleAddSkillModal,
  toggleAddContentModal,
  toggleRemoveSkillModal,
  removeContentFromDevplan,
  togglePhaseUnlockedModal,
  toggleStepUnlockedModal
} from '@actions/developmentPlanActions';
import { AppState } from 'src/app/types/AppState/AppState';
import { Button } from 'centralized-design-system/src/Buttons';
import DevelopmentPlanRemoveSkillModal from '../../modals/DevelopmentPlanRemoveSkillModal/DevelopmentPlanRemoveSkillModal';
import { removeContentFromPlan } from 'edc-web-sdk/requests/developmentPlan';
import { open_v2 as openSnackBar } from '@actions/snackBarActions';
import { FlagTag } from 'centralized-design-system/src/Tags';
import './DevelopmentPlanContent.scss';
import DevPlanPhase from '../DevPlanPhase/DevPlanPhase';
import { isStepReadOnly, shouldEditDevPlanOnFly } from '../../utils';
import { useGetCurrentStep } from '../../hooks/useGetCurrentStep';
import DevPlanStepUnlockedModal from '../../modals/DevPlanStepUnlockedModal/DevPlanStepUnlockedModal';

const DevelopmentPlanContent: React.FC = () => {
  const { data, isDraft, dispatch } = useDevPlan();
  const [skillToRemoveId, setSkillToRemoveId] = useState<DevPlanSkillId>(null);
  const [isOperationPending, setIsOperationPending] = useState(false);
  const contentRefs = useRef<Record<string, HTMLDivElement | null>>({});

  const currentStep = useGetCurrentStep(data);
  const stepContentCnt = countAllContentsForStep(currentStep);
  const isCurrentStepReadOnly = isStepReadOnly(isDraft, currentStep);

  const isSkillsModalOpen = useSelector((state: AppState) =>
    state.developmentPlan.get('isAddSkillModalOpen')
  );
  const isContentModalOpen = useSelector((state: AppState) =>
    state.developmentPlan.get('isAddContentModalOpen')
  );
  const isRemoveSkillModalOpen = useSelector((state: AppState) =>
    state.developmentPlan.get('isRemoveSkillModalOpen')
  );
  const isPhaseUnlockedModalOpen = useSelector((state: AppState) =>
    state.developmentPlan.get('isPhaseUnlockedModalOpen')
  );
  const isStepUnlockedModalOpen = useSelector((state: AppState) =>
    state.developmentPlan.get('isStepUnlockedModalOpen')
  );

  const getFocusReturnSelector = (id: string) => `#dev-plan-phase-skill-${id} .dropdown-btn`;

  const removeContentWithMessage = (learningCardId: string, skill: DevPlanSkill) => {
    dispatch(removeContentFromDevplan(learningCardId, skill.skillId));
    dispatch(
      openSnackBar(
        translatr('web.talentmarketplace.development-plan', 'OpportunityRemovedFromPlan', {
          opportunity: translatr('web.common.main', 'Content')
        }),
        'success',
        false,
        null,
        () => focusNextElement(learningCardId, skill)
      )
    );
  };

  const handleRemoveContent = async (
    learningCardData: LearningCardData,
    skill: DevPlanSkill,
    phase: IDevPlanPhase
  ) => {
    setIsOperationPending(true);
    if (shouldEditDevPlanOnFly(currentStep, phase)) {
      try {
        await removeContentFromPlan(currentStep?.roleId, skill.skillId, learningCardData.id);
        removeContentWithMessage(learningCardData.id, skill);
        setIsOperationPending(false);
      } catch (e) {
        setIsOperationPending(false);
        dispatch(openSnackBar(e?.toString?.(), 'error'));
      }
    } else {
      removeContentWithMessage(learningCardData.id, skill);
      focusNextElement(learningCardData.id, skill);
      setIsOperationPending(false);
    }
  };

  const focusNextElement = (removedId: string, skill: DevPlanSkill) => {
    const contents = skill.contents?.filter(({ status }) => status !== 'REMOVED');
    const ids = contents.map(card => card.id);
    const removedIndex = ids.findIndex(id => id === removedId);

    const nextId = ids[removedIndex + 1];
    const prevId = ids[removedIndex - 1];

    const fallbackId = nextId || prevId;
    const fallbackElement = fallbackId ? contentRefs.current[fallbackId] : null;

    setTimeout(() => {
      const element: HTMLElement =
        fallbackElement?.querySelector('.learning-card-content-details-info-title span') ||
        document.querySelector(getFocusReturnSelector(skill.skillId));
      element?.focus();
    }, 50);
  };

  const prepareAccordionItems = (
    skill: DevPlanSkill,
    phase: IDevPlanPhase
  ): DevPlanAccordionData[] => {
    const { contents, skillLabel, skillName, skillId } = skill;
    const isSkillCompleted =
      skill.status === DevPlanSkillStatus.COMPLETED &&
      currentStep?.status === DevPlanStepStatus.COMPLETED;
    const contentLength =
      contents?.filter(({ status }) => status !== DevPlanContentStatus.REMOVED)?.length || 0;
    const hasAtLeastOneSkill = phase.skills?.length > 1;
    const hasContent = !!contentLength;
    const menuList = isCurrentStepReadOnly
      ? undefined
      : [
          {
            id: '1',
            label: translatr('web.talentmarketplace.development-plan', 'MenuRemoveSkill'),
            action: (e: React.ChangeEvent<HTMLButtonElement>) => {
              setSkillToRemoveId(skill.skillId);
              dispatch(
                toggleRemoveSkillModal(
                  phase,
                  e.currentTarget?.closest('.ed-dropdown')?.querySelector('.dropdown-btn')
                )
              );
            },
            disabled: !hasAtLeastOneSkill,
            tooltipMessage: hasAtLeastOneSkill
              ? null
              : translatr(
                  'web.talentmarketplace.development-plan',
                  'MustHaveAtLeastOneItemInYourPlan'
                )
          },
          {
            id: '2',
            label: translatr('web.talentmarketplace.development-plan', 'MenuAddContent'),
            action: (e: React.ChangeEvent<HTMLButtonElement>) =>
              dispatch(
                toggleAddContentModal(
                  skill,
                  phase,
                  getFocusReturnSelector(skill.skillId),
                  getFocusReturnSelector(skill.skillId)
                )
              )
          }
        ];

    const contentMenu =
      !isSkillCompleted && !isCurrentStepReadOnly
        ? {
            list: [
              {
                label: translatr('web.common.main', 'Remove'),
                action: (data: LearningCardData) => handleRemoveContent(data, skill, phase),
                async: true,
                disabled: () =>
                  stepContentCnt < 2
                    ? translatr(
                        'web.talentmarketplace.development-plan',
                        'MustHaveAtLeastOneItemInYourPlan'
                      )
                    : false
              }
            ]
          }
        : undefined;

    const skillDisplayLabel = skillLabel || skillName || skillId;

    return [
      {
        title: `${skillDisplayLabel} (${contentLength})`,
        className: 'dev-plan__accordion',
        headingLevel: isDraft && !isCurrentStepReadOnly ? 'h3' : 'h4',
        content: (
          <div className="dev-plan__main__content-wrp">
            {contents
              ?.filter(({ status }) => status !== DevPlanContentStatus.REMOVED)
              ?.map((card, idx) => (
                <div
                  key={card.id}
                  className="dev-plan__main__card"
                  ref={el => {
                    contentRefs.current[card.id] = el;
                  }}
                >
                  <LearningCard
                    card={mapResponseToCard(card)}
                    menu={contentMenu}
                    headingLevel="h5"
                  />
                  {idx === contentLength - 1 && !isSkillCompleted && !isCurrentStepReadOnly && (
                    <Button
                      id={`dev-plan-add-content-btn-${skill.skillId}`}
                      size="medium"
                      color="primary"
                      variant="borderless"
                      padding="small"
                      aria-label={`${skillDisplayLabel} ${translatr(
                        'web.talentmarketplace.development-plan',
                        'MenuAddContent'
                      )}`}
                      onClick={() =>
                        dispatch(
                          toggleAddContentModal(
                            skill,
                            phase,
                            `#dev-plan-add-content-btn-${skill.skillId}`,
                            `#dev-plan-add-content-btn-${skill.skillId}`
                          )
                        )
                      }
                    >
                      {translatr('web.talentmarketplace.development-plan', 'MenuAddContent')}
                    </Button>
                  )}
                </div>
              ))}
          </div>
        ),
        HeaderTitleComponent:
          skill.status === DevPlanSkillStatus.COMPLETED
            ? ({ id }) => (
                <FlagTag color="success" id={id}>
                  {translatr('web.common.main', 'Completed')}
                </FlagTag>
              )
            : null,
        headerComponents: hasContent
          ? [
              {
                key: 'menu',
                props: { buttonAriaText: skillDisplayLabel, menuList }
              },
              { key: 'divider' },
              { key: 'arrow' }
            ].filter(({ key }) =>
              (key === 'menu' || key === 'divider') && (isSkillCompleted || isCurrentStepReadOnly)
                ? false
                : true
            )
          : isCurrentStepReadOnly
          ? null
          : [
              {
                key: 'button',
                props: {
                  id: `dev-plan-add-content-button-${skill.skillId}`,
                  text: translatr('web.common.main', 'AddContent'),
                  'aria-label': `${skillDisplayLabel} ${translatr(
                    'web.common.main',
                    'AddContent'
                  )}`,
                  onClick: () =>
                    dispatch(
                      toggleAddContentModal(
                        skill,
                        phase,
                        getFocusReturnSelector(skill.skillId),
                        `#dev-plan-add-content-button-${skill.skillId}`
                      )
                    )
                }
              },
              { key: 'divider' },
              {
                key: 'menu',
                props: { buttonAriaText: skillDisplayLabel, menuList }
              }
            ],
        expandable: hasContent
      }
    ];
  };

  return (
    <div className="dev-plan-content">
      <h2>
        {translatr(
          'web.talentmarketplace.development-plan',
          'ExpandYoutSkillsWithLeearningContent'
        )}
      </h2>
      <div className={`${isOperationPending ? 'dev-plan--operation-pending' : ''}`}>
        <>
          {currentStep.phases.map(phase => {
            const isRelevantPhase =
              phase.status === DevPlanPhaseStatus.STARTED ||
              currentStep.status === DevPlanStepStatus.DRAFT;
            const firstIncompleteSkillIndex = isRelevantPhase
              ? phase.skills?.findIndex(skill => skill.status !== DevPlanSkillStatus.COMPLETED)
              : -1;

            return (
              <DevPlanPhase phase={phase} isDevPlanDraft={isDraft} key={phase.phase}>
                {phase.skills?.map((skill, index) => (
                  <div
                    id={`dev-plan-phase-skill-${skill?.skillId || index}`}
                    key={`plan-accordion-${skill?.skillId + '_' + index || index}`}
                  >
                    <Accordion
                      defaultOpenIndex={
                        isRelevantPhase && index === firstIncompleteSkillIndex ? 0 : -1
                      }
                      items={prepareAccordionItems(skill, phase)}
                    />
                  </div>
                ))}
              </DevPlanPhase>
            );
          })}
          {hasNextPhaseInStep(currentStep) && <DevelopmentPlanNextPhaseUnlock />}
        </>
      </div>
      {isContentModalOpen && (
        <DevelopmentPlanAddContentModal
          roleId={currentStep.roleId}
          closeModal={() => dispatch(toggleAddContentModal(null, null, null))}
        />
      )}
      {isSkillsModalOpen && (
        <DevelopmentPlanAddSkillModal onClose={() => dispatch(toggleAddSkillModal(null))} />
      )}
      {isRemoveSkillModalOpen && (
        <DevelopmentPlanRemoveSkillModal
          id={skillToRemoveId}
          onClose={() => dispatch(toggleRemoveSkillModal(null, null))}
        />
      )}
      {isPhaseUnlockedModalOpen && !isDraft && (
        <DevPlanPhaseUnlockedModal
          roleName={currentStep?.name}
          onClose={() => dispatch(togglePhaseUnlockedModal())}
        />
      )}
      {isStepUnlockedModalOpen && data?.hasTransitionPlan && (
        <DevPlanStepUnlockedModal
          isLastStep={currentStep?.roleId === data?.pathDetails.at(-1)?.roleId}
          roleId={currentStep?.roleId}
          roleName={currentStep?.name}
          onClose={() => dispatch(toggleStepUnlockedModal())}
        />
      )}
    </div>
  );
};

export default DevelopmentPlanContent;
