@import '~styles/_base.scss';
@import '~centralized-design-system/src/Styles/_variables.scss';

.job-form {
  &-wrapper.ed-ui {
    margin: 0 auto;
    padding: var(--ed-spacing-base);

    @media screen and (max-width: $breakpoint-xs) {
      padding: var(--ed-spacing-2xs);
      padding-top: var(--ed-spacing-base);
    }

    @media screen and (min-width: $breakpoint-lg) {
      width: rem-calc(1200);
    }

    #job-form-header {
      color: var(--ed-black);
      font-weight: var(--ed-font-weight-black);
      font-size: var(--ed-font-size-lg);
    }
    .job-form-description {
      font-size: var(--ed-font-size-sm) !important;
      strong {
        color: var(--ed-input-asterisk-color);
      }
    }
    .job {
      &-inner-container {
        max-width: 37.5rem;
        margin: var(--ed-spacing-xl) auto;
      }
      &-form-field {
        margin-top: var(--ed-spacing-lg);

        input[disabled] {
          color: var(--ed-state-disabled-color);
        }
      }
    }
    .optional-text {
      display: none;
    }
    .ed-input-container {
      margin: 0;
    }
    label.ed-input-title {
      font-size: var(--ed-input-font-size);
      font-weight: var(--ed-input-label-font-weight);
    }
    .ed-suggested-skills-container {
      background: none;
      border: var(--ed-border-size-sm) solid var(--ed-border-color);

      .suggested-skills-header {
        h3.ed-input-title {
          color: var(--ed-black);
          font-size: var(--ed-font-size-sm) !important;
          font-weight: var(--ed-font-weight-black);
        }
        .ed-input-description {
          font-size: var(--ed-font-size-sm);
        }
      }
      .tag-box {
        button.dropdown-btn {
          font-size: var(--ed-tag-font-size);
          font-weight: var(--ed-tag-font-weight);
          background-color: var(--ed-tag-bg-color);
          border: var(--ed-tag-border-size) solid var(--ed-border-color);
          border-radius: var(--ed-tag-border-radius);
          padding: var(--ed-tag-padding-y) var(--ed-tag-padding-x);
          &:hover {
            .icon-plus-circle {
              color: var(--ed-primary-base);
            }
          }
        }
      }
    }

    .text-field-meta-group {
      display: flex;
      justify-content: space-between;
      font-size: var(--ed-font-size-sm);

      &.input-error {
        * {
          color: var(--ed-negative-1);
        }
      }
    }
    .has-error {
      .ed-multi-select__control {
        border: var(--ed-border-size-sm) solid var(--ed-negative-2) !important;
      }
    }

    .search-meta {
      color: var(--ed-text-color-supporting);
      text-align: right;
    }

    .project-form-field {
      margin-top: var(--ed-spacing-lg);
    }

    .ed-ui.ed-skill-section {
      margin-bottom: var(--ed-spacing-lg);
      h2.ed-input-title {
        font-size: var(--ed-input-font-size);
        font-weight: var(--ed-input-label-font-weight);
      }
    }

    .skill-level-container {
      display: flex;
      align-items: flex-end;
      justify-content: space-between;

      .ed-select-label {
        margin-bottom: var(--ed-spacing-4xs);
      }
      .skill-search-input {
        width: rem-calc(278);
        margin-right: var(--ed-spacing-2xs);
        .ed-multi-select__control {
          height: rem-calc(40);
        }
      }
      .level-select-input {
        width: rem-calc(186);
        margin-right: var(--ed-spacing-2xs);
        select {
          height: rem-calc(40);
        }
      }
      .ed-btn-v2 {
        height: rem-calc(40);
        width: rem-calc(100) !important;
      }
    }

    .job-roles-tooltip {
      &,
      & > div {
        width: 100%;
      }
    }

    .ed-skill-list {
      margin-left: 0;

      .ed-tag-list {
        display: inline-flex;
        max-width: 100%;
      }
    }
  }

  &-container {
    height: auto;
    margin-top: var(--ed-spacing-base);
  }
  &-footer-actions {
    display: flex;
    justify-content: center;
    margin-top: rem-calc(40);

    @media screen and (max-width: $breakpoint-xs) {
      flex-direction: column;
      gap: var(--ed-spacing-xs);

      .ed-btn {
        margin: 0;
      }
    }
  }
}
