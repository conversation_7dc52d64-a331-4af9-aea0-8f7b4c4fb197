import React, { useCallback, useState, useEffect } from 'react';
import { connect } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import PropTypes from 'prop-types';
import { translatr, omp, ompLov } from 'centralized-design-system/src/Translatr';
import {
  updateJob,
  CAPABILITY_STATUS,
  ROLES_STATUS,
  JOB_TYPE
} from 'edc-web-sdk/requests/careerOportunities.v2';
import { getLevelByDecimal } from 'centralized-design-system/src/Utils/proficiencyLevels';
import TextField from 'centralized-design-system/src/Inputs/TextField';
import { AsyncSearchInput } from 'centralized-design-system/src/Inputs';
import { open_v2 as openSnackBar } from '../../../../app/actions/snackBarActions';
import {
  Fields,
  MAX_ASSIGNED_SKILLS_DEFAULT,
  MAX_ASSIGNED_LINKED_ROLES_DEFAULT,
  MODULES,
  SECTIONS
} from './types';
import Backarrow from '@components/backarrow';
import { Field, Form } from '../../Projects/ProjectForm/Form';
import Modal from 'centralized-design-system/src/Modals';
import ConfirmationModal from '@components/modals/ConfirmationModalV2';
import './styles.scss';
import SkillWrapper from '../../Projects/ProjectForm/SkillWrapper';
import { MAP_OLD_LEVELS_TO_DECIMAL } from '@components/modals/SkillsModal/constant';
import { shouldShowTMJobRole } from 'opportunity-marketplace/util';
import Tooltip from 'centralized-design-system/src/Tooltip';
import { jobRolesValidator } from 'opportunity-marketplace/shared/validator';
import { removeMemoizedMatchingJobByID } from '@actions/memoizedMatchingJobsActions';
import { DEFAULT_PROFILE_SKILL_DETECTION_LEVEL } from '@components/UserSkillsForm/utils';
import { Button } from 'centralized-design-system/src/Buttons';
import { safeRender } from 'edc-web-sdk/requests/renderingOptions';

const EditJobForm = ({
  currentUserLang,
  data = {},
  title,
  description,
  jobId,
  maxAssignedSkills,
  maxAssignedLinkedRoles,
  module = MODULES.CAREER,
  section,
  toast,
  removeMemoizedMatchingJob
}) => {
  const status = data.status;
  const navigate = useNavigate();

  const [fields, setFields] = useState({
    [Fields.SKILLS]: data.skills || [],
    [Fields.TITLE]: data.title || '',
    [Fields.JOB_ROLES]: data.jobRoles || []
  });

  const [errors, setErrors] = useState({
    [Fields.JOB_ROLES]: []
  });

  const [updating, setUpdating] = useState(false);
  const [showCancelConfirmation, setShowCancelConfirmation] = useState(false);
  const [isInitialDataEdited, setIsInitialDataEdited] = useState(false);
  const [initialRoles] = useState(data.jobRoles || []);
  const [initialSkills] = useState(data.skills || []);

  useEffect(() => {
    const isStateEdited =
      isNewStateDifferentFromInitState(initialSkills, fields[Fields.SKILLS], skillsToKeys) ||
      isNewStateDifferentFromInitState(initialRoles, fields[Fields.JOB_ROLES], rolesToKeys);
    setIsInitialDataEdited(isStateEdited);

    /* JOB_ROLES Validation - only for manually added linked roles*/
    if (shouldShowTMJobRole() && !data.isLinkedRolesPredefined) {
      const newErrors = {
        ...errors,
        [Fields.JOB_ROLES]:
          jobRolesValidator(maxAssignedLinkedRoles, fields[Fields.JOB_ROLES]) || ''
      };
      setErrors(newErrors);
    }
  }, [fields[Fields.SKILLS], fields[Fields.JOB_ROLES], data.isLinkedRolesPredefined]);

  const updateFieldValue = useCallback(
    (fieldId, value) => {
      setFields(f => ({ ...f, [fieldId]: value }));
    },
    [setFields, setIsInitialDataEdited]
  );

  const isNewStateDifferentFromInitState = (initState, newState, arrayToKeys) => {
    if (initState.length !== newState.length) {
      return true;
    }

    const sortedInitKeys = arrayToKeys(initState).sort();
    const sortedNewKeys = arrayToKeys(newState).sort();

    for (let i = 0; i < sortedInitKeys.length; i++) {
      if (sortedInitKeys[i] !== sortedNewKeys[i]) {
        return true;
      }
    }

    return false;
  };

  const skillsToKeys = skills => {
    return skills.map(({ id, level }) => id + '_' + level);
  };

  const rolesToKeys = roles => {
    return roles.map(role => role.id);
  };

  const updateRolesFieldValue = useCallback(
    (fieldId, value) => {
      const val = value.map(v => {
        if (v.name) {
          return {
            id: v.id,
            value: v.id,
            label: v.label,
            status: CAPABILITY_STATUS.DECLARED
          };
        }
        return v;
      });
      updateFieldValue(fieldId, val);
    },
    [updateFieldValue]
  );

  const createPayload = useCallback(({ capabilities, jobRoles }) => {
    const removedRoles = initialRoles
      .filter(x => !jobRoles.find(job => job.id === x.id))
      .map(j => ({
        internalId: j.id,
        linkedRoleStatus: ROLES_STATUS.REMOVED
      }));

    // when JOB ROLES module is deactivated we need to send null value for linkedRoles to avoid changing data under thar property... null will not change current data
    const linkedRoles = shouldShowTMJobRole()
      ? jobRoles
          .map(role => ({
            internalId: role.id,
            linkedRoleStatus:
              Object.values(ROLES_STATUS).indexOf(role.status) !== -1 ? role.status : ''
          }))
          .concat(removedRoles)
      : null;

    return {
      capabilities: capabilities.map(skill => ({
        id: skill.id,
        name: skill.name,
        label: skill.label,
        level: null,
        proficiencyLevel: skill.proficiencyLevel,
        skillSourceId: skill.skillSourceId,
        status: skill.status,
        type: skill.type
      })),
      linkedRoles: linkedRoles
    };
  });

  const checkSkillStatus = skill => {
    if (!skill?.status) return CAPABILITY_STATUS.DECLARED;

    const skillAlreadyDeclared =
      initialSkills?.find(({ id }) => skill.id === id)?.status === CAPABILITY_STATUS.DECLARED;
    if (skillAlreadyDeclared) {
      return CAPABILITY_STATUS.DECLARED;
    }

    return skill.status;
  };

  const prepareLevel = (skill = { level: MAP_OLD_LEVELS_TO_DECIMAL.intermediate }) =>
    parseFloat(skill?.level) === -1 ? null : getLevelByDecimal(skill.level)?.level;

  const getCapabilitiesPayload = () => {
    const capabilities = fields[Fields.SKILLS].map(skill => ({
      ...skill,
      name: skill.name,
      skillSourceId: skill.skillSourceId || skill.name,
      type: 'SKILL',
      status: CAPABILITY_STATUS[checkSkillStatus(skill).toUpperCase()],
      proficiencyLevel: {
        decimal: prepareLevel(skill)
      }
    }));

    const removedDetectedSkills = initialSkills
      .filter(s => s.status === CAPABILITY_STATUS.DETECTED)
      .filter(({ id }) => !capabilities.find(capability => capability.id === id))
      .map(skill => ({
        ...skill,
        skillSourceId: skill.skillSourceId || skill.name,
        proficiencyLevel: { decimal: prepareLevel(skill) },
        status: CAPABILITY_STATUS.REMOVED
      }));

    return [...capabilities, ...removedDetectedSkills];
  };

  const showMaxSkillsWarning = maxSkills =>
    toast(
      translatr('web.talentmarketplace.main', 'YouReachedTheMaximumNumberOfSkills', {
        maxSkills
      }),
      'warning'
    );

  const onUpdateClick = useCallback(() => {
    if (fields[Fields.SKILLS].length > maxAssignedSkills) {
      return showMaxSkillsWarning(maxAssignedSkills);
    }

    const formValues = {
      capabilities: getCapabilitiesPayload(),
      title: fields[Fields.TITLE],
      jobRoles: fields[Fields.JOB_ROLES]
    };

    setUpdating(true);

    updateJob(jobId, createPayload(formValues, status))
      .then(job => {
        removeMemoizedMatchingJob(job.id);
        setUpdating(false);
        setIsInitialDataEdited(false);
        toast(
          translatr('web.talentmarketplace.main', 'OpportunitySavedSuccessfully', {
            opportunity: omp('tm_job_vacancy')
          })
        );
        navigate(-1);
      })
      .catch(() => {
        setUpdating(false);
        toast(
          translatr(
            'web.talentmarketplace.main',
            'ThereWasAnErrorWhileTryingToSaveYourOpportunityPleaseTryAgain',
            {
              opportunity: omp('tm_job_vacancy')
            }
          ),
          'error'
        );
      });
  }, [fields, setUpdating, toast]);

  const closeModal = useCallback(() => setShowCancelConfirmation(false), [
    setShowCancelConfirmation
  ]);

  const onConfirm = useCallback(() => {
    setShowCancelConfirmation(false);
    navigate(-1);
  }, [history]);

  const onCancelClick = useCallback(() => {
    if (isInitialDataEdited) {
      setShowCancelConfirmation(true);
    } else {
      navigate(-1);
    }
  }, [history, setShowCancelConfirmation, isInitialDataEdited]);

  const defaultProficiencySkillLevel = getLevelByDecimal(DEFAULT_PROFILE_SKILL_DETECTION_LEVEL)
    ?.level;

  if (
    !Object.values(MODULES).includes(module) ||
    (module === MODULES.SOURCING && !Object.values(SECTIONS).includes(section))
  )
    return null;

  const formTitle =
    module === MODULES.SOURCING
      ? section === SECTIONS.ROLES
        ? `${translatr('web.sourcing.candidate-profile', 'EditLinked', {
            labelName: ompLov('tm_job_roles')
          })}`
        : translatr('web.sourcing.candidate-profile', 'EditRelatedSkills')
      : translatr('web.talentmarketplace.main', 'EditOpportunity', {
          opportunity: omp('tm_job_vacancy')
        });
  const showSkills =
    module === MODULES.CAREER ? true : module === MODULES.SOURCING && section === SECTIONS.SKILLS;
  const showLinkedRoles =
    module === MODULES.CAREER ? true : module === MODULES.SOURCING && section === SECTIONS.ROLES;
  const jobRoleTitle = module === MODULES.CAREER ? omp('tm_tm_job_roles') : '';
  const skillsTitle =
    module === MODULES.CAREER ? translatr('web.projects.main', 'SelectSkills') : '';

  return (
    <div className="ed-ui job-form-wrapper">
      <Backarrow
        goToPrevPage
        needWarningModal={isInitialDataEdited}
        label={translatr('web.common.main', 'Back')}
        pageHistory={[]}
      />
      <div className="job-form-container block no-bottom-radius">
        <div className="job-inner-container">
          <h1 id="job-form-header" className="text-center">
            {formTitle}
          </h1>
          {module === MODULES.CAREER && (
            <p
              className="job-form-description"
              dangerouslySetInnerHTML={{
                __html: safeRender(
                  translatr('web.common.main', 'FieldsMarkedWithAsteriskAreRequired', {
                    asterisk: `<strong>*</strong>`
                  })
                )
              }}
            ></p>
          )}
          <Form id="create-edit-form" aria-labelledby="job-form-header">
            {module === MODULES.CAREER && (
              <Field
                required
                error={errors[Fields.TITLE]}
                id="job-title"
                length={fields[Fields.TITLE].length}
                suffix="left"
                title={translatr('web.talentmarketplace.main', 'OpportunityTitle', {
                  opportunity: omp('tm_job_vacancy')
                })}
                type="text"
                fieldClass="job-form-field job-form-field--disabled"
              >
                <TextField
                  required
                  placeholder={translatr(
                    'web.talentmarketplace.main',
                    'EditOpportunityEnterTitle',
                    {
                      opportunity: omp('tm_job_vacancy')
                    }
                  )}
                  setValue={val => updateFieldValue(Fields.TITLE, val)}
                  defaultValue={fields[Fields.TITLE]}
                  disabled={true}
                />
              </Field>
            )}
            {showSkills && (
              <Field
                length={fields[Fields.SKILLS].length}
                error={errors[Fields.SKILLS]}
                suffix="Skills"
                title={skillsTitle}
              >
                <SkillWrapper
                  suggestionsPayload={{
                    title,
                    description,
                    culture: global?.__ED__?.profile?.language
                  }}
                  defaultProficiencySkillLevel={defaultProficiencySkillLevel}
                  skillsItem={fields[Fields.SKILLS]}
                  updateFieldValue={updateFieldValue}
                  module={module}
                />
              </Field>
            )}
            {shouldShowTMJobRole() && showLinkedRoles && (
              <Field
                error={!data.isLinkedRolesPredefined ? errors[Fields.JOB_ROLES] : false}
                id="job-roles-input"
                length={fields[Fields.JOB_ROLES].length}
                maxLength={!data.isLinkedRolesPredefined ? maxAssignedLinkedRoles : undefined}
                suffix={omp('tm_tm_job_roles')}
                title={jobRoleTitle}
                fieldClass="job-form-field"
              >
                <Tooltip
                  message={translatr(
                    'web.talentmarketplace.main',
                    'LinkedRoleProvidedByIntegration'
                  )}
                  pos="top"
                  customClass="job-roles-tooltip"
                  hide={!data.isLinkedRolesPredefined}
                >
                  <AsyncSearchInput
                    id="job-roles-input"
                    defaultAriaLabel={formTitle}
                    ariaLabelledby="job-roles-input-label job-roles-input-count"
                    multiselect
                    roles
                    users={false}
                    channels={false}
                    groups={false}
                    items={fields[Fields.JOB_ROLES]}
                    placeholder={translatr('web.talentmarketplace.main', 'SearchOpportunities', {
                      opportunities: omp('tm_tm_job_roles')
                    })}
                    onChange={val => updateRolesFieldValue(Fields.JOB_ROLES, val)}
                    disabled={!data.jobRolesEditable}
                    extraData={{ currentUserLang }}
                  />
                </Tooltip>
              </Field>
            )}

            <div className="job-form-footer-actions">
              <Button variant="ghost" color="secondary" disabled={updating} onClick={onCancelClick}>
                {translatr('web.talentmarketplace.main', 'Cancel')}
              </Button>
              <Button
                color="primary"
                disabled={
                  !isInitialDataEdited ||
                  updating ||
                  (module === MODULES.CAREER && errors[Fields.JOB_ROLES].length) ||
                  (module === MODULES.SOURCING &&
                    section === SECTIONS.ROLES &&
                    errors[Fields.JOB_ROLES].length)
                }
                onClick={onUpdateClick}
              >
                {updating
                  ? translatr('web.talentmarketplace.main', 'Saving')
                  : translatr('web.talentmarketplace.main', 'Save')}
              </Button>
              {showCancelConfirmation && isInitialDataEdited && (
                <Modal size="small">
                  <ConfirmationModal
                    isNegativeValue
                    noNeedClose
                    message={translatr('web.talentmarketplace.main', 'YouAreAboutToDiscardChanges')}
                    confirmBtnTitle={translatr('web.talentmarketplace.main', 'Discard')}
                    callback={onConfirm}
                    cancelClick={closeModal}
                    closeModal={closeModal}
                    title={translatr('web.talentmarketplace.main', 'DiscardChanges')}
                  />
                </Modal>
              )}
            </div>
          </Form>
        </div>
      </div>
    </div>
  );
};

EditJobForm.propTypes = {
  title: PropTypes.string,
  description: PropTypes.string,
  currentUserLang: PropTypes.string,
  data: PropTypes.object,
  jobId: PropTypes.string,
  maxAssignedSkills: PropTypes.number,
  maxAssignedLinkedRoles: PropTypes.number,
  module: PropTypes.string,
  section: PropTypes.string,
  toast: PropTypes.func,
  removeMemoizedMatchingJob: PropTypes.func
};

export default connect(
  ({ currentUser, team }) => ({
    maxAssignedSkills:
      team?.get?.('ecsConfig')?.toJS?.()?.[JOB_TYPE.VACANCY]?.max_assigned_skills ||
      MAX_ASSIGNED_SKILLS_DEFAULT,
    maxAssignedLinkedRoles:
      team?.get?.('ecsConfig')?.toJS?.()?.[JOB_TYPE.VACANCY]?.max_assigned_linked_roles ||
      MAX_ASSIGNED_LINKED_ROLES_DEFAULT,
    currentUserLang:
      currentUser.get('profile')?.get?.('language') ||
      currentUser.get('profile')?.language ||
      team?.get('config')?.DefaultOrgLanguage ||
      'en'
  }),
  dispatch => ({
    toast: (message, type = 'success') => dispatch(openSnackBar(message, type)),
    removeMemoizedMatchingJob: id => dispatch(removeMemoizedMatchingJobByID(JOB_TYPE.VACANCY, id))
  })
)(EditJobForm);
