import * as actionTypes from '../constants/actionTypes';
import { usersv2 } from 'edc-web-sdk/requests';
import { users, groups } from 'edc-web-sdk/requests/index';
import { setToken, JWT } from 'edc-web-sdk/requests/csrfToken';
import { getUserNotificationConfig } from 'edc-web-sdk/requests/orgSettings';
import { getUserJobRole } from 'edc-web-sdk/requests/users.v2';
import { tr } from 'edc-web-sdk/helpers/translations';
import { Permissions } from '../utils/checkPermissions';
import _ from 'lodash';
import { guessUserTimezone, setTokensInOnboarding } from '@utils/utils';
import { MYGUIDE_URL } from 'edc-web-sdk/config/envConstants';
import { checkMfaFlow } from '../utils';
import {
  AFTER_LOGIN_CONTENT_URL,
  AFTER_ONBOARDING_URL,
  LOGIN_WEB_SESSION_TIMEOUT,
  TEST_KEY
} from '../constants/localStorageConstants';

export function requestUserIsGroupLeader() {
  return dispatch => {
    const payload = { role: 'admin', fields: 'id' };
    groups
      .getItems(payload)
      .then(groupsData => {
        const state = groupsData.length > 0;

        dispatch({
          type: actionTypes.RECEIVE_GROUPLEADER_USER_INFO,
          state
        });
      })
      .catch(error =>
        console.error(`ERROR!! on currentUserAction.requestUserIsGroupLeader: ${error}`)
      );
  };
}

export function isLocalStorageSupported() {
  try {
    localStorage.setItem(TEST_KEY, '1');
    localStorage.removeItem(TEST_KEY);
    return true;
  } catch (error) {
    return false;
  }
}

export function getSpecificUserInfo(params, currentUser = {}) {
  return async dispatch => {
    let diffKeys = params.filter(param => {
      if (!currentUser.hasOwnProperty(param)) {
        return param;
      }
    });

    if (_.isEmpty(diffKeys)) {
      return;
    }
    /* the key of this paramsMapping object will be the keys for the current reducer
     * and the values are the payload parameters keys */
    // followedChannels might be having similar data with followingChannels or writableChannels.
    // followingChannels doesn't seems to be working fine, we might need to remove it.
    // Keeping now for backward compatibility
    let paramsMapping = {
      first_name: 'first_name',
      last_name: 'last_name',
      bio: 'bio',
      userSubscriptions: 'user_subscriptions',
      handle: 'handle',
      followingChannels: 'following_channels',
      followersCount: 'followers_count',
      followingCount: 'following_count',
      defaultTeamId: 'default_team_id',
      roles: 'roles',
      rolesDefaultNames: 'roles_default_names',
      writableChannels: 'writable_channels, channels(id,label,is_private,slug)',
      coverImage: 'coverimages',
      hideFromLeaderboard: 'hide_from_leaderboard',
      onboardingCompletedDate: 'onboarding_completed_date',
      dashboardInfo: 'dashboard_info',
      company: 'company',
      jobFamily: 'job_role',
      userRoles: 'user_roles',
      customFields: 'custom_fields',
      manager: 'manager',
      reporters: 'reporters',
      externalId: 'external_id',
      location: 'location',
      followedTeams: 'followed_teams,teams(id,name)',
      followedChannels: 'followed_channels,channels(id,label,slug)',
      skillAssessmentAccessible: 'skill_assessment_accessible',
      organization_units: 'organization_units'
    };

    let finalParamsMapping = _.pick(paramsMapping, diffKeys);
    let mappedParam = _.map(finalParamsMapping, val => {
      return val;
    });
    let payload = { fields: _.map(mappedParam).join(',') };
    let data;
    let finalReducerValues;

    if (!_.isEmpty(diffKeys)) {
      try {
        data = await users.getUserInfo(payload);

        let userData = {
          first_name: data.firstName,
          last_name: data.lastName,
          userSubscriptions: data.userSubscriptions?.[0] || null,
          bio: data.bio,
          hideFromLeaderboard: data.hideFromLeaderboard,
          handle: data.handle && data.handle.substr(1),
          followingChannels: data.followingChannels || [],
          followersCount: data.followersCount,
          followingCount: data.followingCount,
          defaultTeamId: data.defaultTeamId,
          roles: data.roles?.map(role => role.toLowerCase()),
          rolesDefaultNames: data.rolesDefaultNames,
          writableChannels: data.writableChannels,
          onboardingCompletedDate: data.onboardingCompletedDate,
          company: data.company,
          coverImage: data.coverimages?.banner_url || data.coverimages?.banner,
          jobFamily: data.jobRole,
          userRoles: data.userRoles,
          customFields: data.customFields,
          manager: data.manager,
          reporters: data.reporters || [],
          externalId: data.externalId,
          location: data.location,
          followedChannels: data.followedChannels || [],
          followedTeams: data.followedTeams || [],
          skillAssessmentAccessible: data.skillAssessmentAccessible,
          organization_units: data.organizationUnits || [],
          additionalLanguages: data.profile?.additional_languages || []
        };

        finalReducerValues = _.map(finalParamsMapping, (val, key) => {
          return key;
        });
        userData = _.pick(userData, finalReducerValues);
        dispatch({
          type: actionTypes.SET_CURRENT_USER_INFO,
          userData
        });
        if (data.hasOwnProperty('dashboardInfo')) {
          dispatch({
            type: actionTypes.UPDATE_PUBLIC_PROFILE_INFO,
            dashboardInfo: data.dashboardInfo
          });
        }
        return userData;
      } catch (error) {}
    }
  };
}

export function isLocationContentURL(path) {
  const locationSubstrings = [
    '/pathways/',
    '/insights/',
    '/mobile-insights/',
    '/journey/',
    '/channel/',
    '/channels/',
    '/smartsearch',
    '/video_streams/',
    '/teams/',
    '/discover',
    '/curate',
    '/notifications',
    '/settings/triggers',
    '/@',
    '/org-groups/admin',
    '/my-assignments',
    '/skills-assessment',
    '/career',
    '/feed',
    '/me/skills-passport',
    '/mkp-redirect',
    '/team',
    '/me'
  ];
  const pathname = path || window.location.pathname;
  return locationSubstrings.some(s => pathname.includes(s));
}

export function initUserData(orgInfo, navigate) {
  return async (dispatch, getState) => {
    try {
      // default user userInfo returns limited field hence need these call to get other details which are required througout the application later.
      let specificUserInfoFields = [];
      const currentUserRoles = getState().currentUser.get('roles'),
        writableChannels = getState().currentUser.get('writableChannels'),
        currentUserJobFamily = getState().currentUser.get('jobFamily'),
        followedChannels = getState().currentUser.get('followedChannels'),
        followedTeams = getState().currentUser.get('followedTeams'),
        manager = getState().currentUser.get('manager'),
        skillAssessmentAccessible = getState().currentUser.get('skillAssessmentAccessible'),
        externalID = getState().currentUser.get('externalId');

      !externalID && specificUserInfoFields.push('externalId');

      !skillAssessmentAccessible && specificUserInfoFields.push('skillAssessmentAccessible');

      (!currentUserJobFamily || !writableChannels) &&
        specificUserInfoFields.push('writableChannels', 'roles', 'rolesDefaultNames');

      !followedChannels && specificUserInfoFields.push('followedChannels');
      !followedTeams && specificUserInfoFields.push('followedTeams');
      !manager && specificUserInfoFields.push('manager');

      // Setting as a promise array to fire at the same time
      let promises = [users.getUserInfo()];
      if (specificUserInfoFields.length > 0) {
        // This works because it is returning an async dispatch that returns userData
        promises.push(dispatch(getSpecificUserInfo(specificUserInfoFields)));
      }

      // Instead of checking for user data, we throw an error into the catch block
      let results = await Promise.all(promises);
      let user = { ...results[0], ...(results[1] || {}) };
      if (!currentUserRoles) {
        // Once LaunchDarkly is ready
        window.ldclient.waitUntilReady
          .then(() => {
            const isCareerGrowth = getState().team?.get('config')?.hr_data_service_enablement;
            const userProfile = user.profile;
            const orgLang = orgInfo?.configs?.find?.(a => a.name === 'DefaultOrgLanguage')?.value;
            const currentUserLang = userProfile?.language || orgLang || 'en';
            getUserJobRole({ career_growth: isCareerGrowth, language: currentUserLang })
              .then(data => {
                const userData = { jobFamily: data };
                dispatch({
                  type: actionTypes.SET_CURRENT_USER_INFO,
                  userData
                });
              })
              .catch(({ err, resp }) => {
                console.error(`Error in currentUserActions.getUserJobRole: ${err}`);
                if (resp?.statusCode === 422) {
                  // No Job Role record for user.
                  const userData = { jobFamily: null };
                  dispatch({
                    type: actionTypes.SET_CURRENT_USER_INFO,
                    userData
                  });
                }
              });
          })
          .catch(err => {
            console.error(`Error in currentUserActions.ldclient.waitUntilReady: ${err}`);
          });
      }

      // below code is for MFA flow
      const oktaEnabled = orgInfo?.configs?.find?.(a => a.name === 'app.config.okta.enabled')
        ?.value;
      checkMfaFlow(navigate, oktaEnabled, user?.passwordChangeable);
      // MFA checks ends here.

      if (!user || !user.csrfToken) return;
      setToken(user.csrfToken);
      setTokensInOnboarding(user.csrfToken, user.jwtToken);
      if (
        (localStorage.getItem(LOGIN_WEB_SESSION_TIMEOUT) &&
          localStorage.getItem(LOGIN_WEB_SESSION_TIMEOUT) > Date.now()) ||
        !localStorage.getItem(LOGIN_WEB_SESSION_TIMEOUT)
      ) {
        JWT.token = user.jwtToken;
        let webSessionTimeout =
          user.organization?.configs && user.organization['web_session_timeout'];
        webSessionTimeout = webSessionTimeout ? webSessionTimeout * 60 * 1000 + Date.now() : 0;
        localStorage.setItem(LOGIN_WEB_SESSION_TIMEOUT, webSessionTimeout);
      } else if (localStorage.getItem(LOGIN_WEB_SESSION_TIMEOUT) === '0') {
        JWT.token = user.jwtToken;
      } else if (localStorage.getItem(LOGIN_WEB_SESSION_TIMEOUT)) {
        localStorage.removeItem(LOGIN_WEB_SESSION_TIMEOUT);
      }

      Permissions.enabled = user.permissions;
      if (!user.profile?.language?.length || !user.profile?.timeZoneDetails?.defaultName) {
        const payload = {
          language: user.profile?.language || window.navigator.language || 'en',
          time_zone: user.profile?.timeZoneDetails?.defaultName || guessUserTimezone()
        };
        const post_info = await users.putUserProfile(payload, user.id);
        user.profile = {
          ...user.profile,
          language: post_info?.profile?.language || user.profile?.language,
          timeZone: post_info?.profile?.timeZone || user.profile?.timeZone,
          timeZoneDetails: post_info?.profile?.timeZoneDetails || user.profile?.timeZoneDetails
        };
      }

      if (!skillAssessmentAccessible) {
        user = {
          ...user,
          skillAssessmentAccessible: getState().currentUser.get('skillAssessmentAccessible')
        };
      }

      dispatch({
        type: actionTypes.RECEIVE_INIT_USER_INFO,
        user
      });

      return user;
    } catch (error) {
      let url = window.location.pathname;
      url = window.location.search ? url + window.location.search : url;

      if (isLocalStorageSupported() && isLocationContentURL()) {
        localStorage.setItem(AFTER_LOGIN_CONTENT_URL, url);
        localStorage.setItem(AFTER_ONBOARDING_URL, url);
      }
      console.error('init user', error.stack);
      dispatch({
        type: actionTypes.ERROR_INIT_USER,
        error
      });
    }
  };
}

export function getWalletBalance() {
  return async dispatch => {
    const walletbalance = await usersv2.getUserBalance();
    dispatch({
      type: actionTypes.RECEIVE_WALLET_BALANCE,
      walletbalance
    });
  };
}

export function requestLogin(email, password) {
  return dispatch => {
    return users
      .requestLogin(email, password)
      .then(user => {
        setToken(user.csrfToken);
        setTokensInOnboarding(user.csrfToken, user.jwtToken);
        JWT.token = user.jwtToken;
        Permissions.enabled = user.permissions;
        // groups.getList(undefined, 0, 'admin').then((groupsData) => {
        //   user.isGroupLeader = groupsData.length > 0;
        //   dispatch({
        //     type: actionTypes.RECEIVE_USER_AUTHENTICATED,
        //     user
        //   });
        // }).catch((err) => {
        //     console.error(`Error in currentUserActions.getList.func: ${err}`);
        // });
        return user;
      })
      .catch(error => {
        let errorMsg = tr(JSON.parse(error.response.text).error);
        dispatch({
          type: actionTypes.ERROR_USER_LOGIN,
          error,
          errorMsg
        });
      });
  };
}

export function updateFollowingUsersCount(state) {
  return dispatch => {
    dispatch({
      type: actionTypes.UPDATE_FOLLOWING_USERS_COUNT,
      state
    });
  };
}

export function updateUserInfo(currentUser, newUser) {
  return dispatch => {
    return users
      .updateUser(currentUser.id, newUser)
      .then(data => {
        let user = _.merge(currentUser, data);
        if (user.profile && data.profile) {
          user.profile.additionalLanguages = data.profile.additionalLanguages; //_.merge joins arrays, causing this field to retain old values
        }

        if (data.avatarimages?.medium) {
          user.picture = data.avatarimages.medium;
        }
        if (data.coverimages?.medium) {
          user.coverimages = data.coverimages;
        } else {
          user.coverimages.banner = currentUser.coverImage;
        }
        if (data.profile?.company) {
          user.company = data.profile.company;
        }
        dispatch({
          type: actionTypes.UPDATE_USER_DETAILS,
          user
        });
        dispatch({
          type: actionTypes.UPDATE_USER_EXPERTISE_AND_INTEREST,
          profile: user.profile
        });
        return user;
      })
      .catch(error => {
        dispatch({
          type: actionTypes.ERROR_UPDATED_USER_INFO,
          error
        });
        return error;
      });
  };
}

export function newRequestLogin(email, password, termsAccepted) {
  return () => {
    return users
      .newRequestLogin(email, password, termsAccepted)
      .then(user => {
        setToken(user.csrfToken);
        setTokensInOnboarding(user.csrfToken, user.jwtToken);
        JWT.token = user.jwtToken;
        Permissions.enabled = user.permissions;

        return user;
      })
      .catch(response => {
        if (response.statusCode === 429) {
          return response;
        } else {
          return response.body;
        }
      });
  };
}

export function getNotificationConfigForUser() {
  return dispatch => {
    return getUserNotificationConfig().then(response => {
      const showNotificationTab = showNotificationTabForUser(
        response.user_config,
        response.org_config
      );
      dispatch({
        type: actionTypes.FETCH_NOTIFICATION_CONFIG,
        notificationConfig: response,
        showNotificationTab
      });
      return { ...response, ...{ showNotificationTab } };
    });
  };
}

export function showNotificationTabForUser(userConfig, orgConfig) {
  let showTab = false;
  userConfig.groups.map(group => {
    group.notifications &&
      group.notifications.map(notification => {
        userConfig.mediums &&
          userConfig.mediums.map(() => {
            if (orgConfig.options[notification.id].user_configurable) {
              showTab = true;
            }
          });
      });
  });
  return showTab;
}

export function loadGuideMe(configs) {
  let { enable_guide_me: enableGuideMe, guide_me_config: guideMeConfig } = configs;
  const encryptedOrgKey = guideMeConfig?.encrypted_org_key || '';
  guideMe(enableGuideMe, encryptedOrgKey);
}

function guideMe(enableGuideMe, encryptedOrgKey) {
  const myGuideScript = document.createElement('script');

  if (enableGuideMe) {
    myGuideScript.src = MYGUIDE_URL;
    window.myGuideOrgKey = encryptedOrgKey;
  }
  myGuideScript.defer = true;
  document.body.appendChild(myGuideScript);
  window.guideMe = window.guideMe || {};
}

export function updateGtcConfirmationModal() {
  return {
    type: actionTypes.UPDATE_GTC_CONFIRMATION_MODAL
  };
}

export function updateIsFactorEnrolled(isFactorEnrolled) {
  return {
    type: actionTypes.UPDATE_IS_FACTOR_ENROLLED,
    isFactorEnrolled
  };
}

export function updateUserProfile(profile) {
  return {
    type: actionTypes.UPDATE_USER_PROFILE,
    profile
  };
}
