/**
 * @module server/config/router
 */
// Elastic Search APM
const apm = require('elastic-apm-node').start({
  serviceName: process.env.ELASTIC_APM_SERVICE_NAME || 'edc-web',
  secretToken: process.env.ELASTIC_APM_SECRET_TOKEN || '',
  apiKey: process.env.ELASTIC_APM_API_KEY || '',
  serverUrl: process.env.ELASTIC_APM_SERVER_URL || 'http://localhost:8200',
  active: process.env.NODE_ENV !== 'development',
  maxQueueSize: parseInt(process.env.ELASTIC_APM_MAX_QUEUE_SIZE, 10) || 1024
});
var express = require('express');
var router = express.Router();
var path = require('path');
var request = require('superagent');
const https = require('https');
var getCspData = require('edc-web-sdk/requests/csp');
var getStringifyData = require('edc-web-sdk/helpers/csp');
var cspPolicy = '';

module.exports = function(app, logger) {
  var envVars = app.configMgr.getClientEnvVars();
  envVars.NODE_ENV = process.env.NODE_ENV;

  envVars.JsPublic = process.env.JS_PUBLIC_KEY;
  envVars.TRANSLATION_DOMAIN =
    process.env.TRANSLATION_DOMAIN || 'https://dol6fvsidtwsq.cloudfront.net';
  envVars.AWS_CDN_STATIC_ASSETS_HOST =
    process.env.AWS_CDN_STATIC_ASSETS_HOST || 'https://dp598loym07sk.cloudfront.net';

  envVars.FILESTACK_APIKEY = process.env.FILESTACK_APIKEY;
  envVars.FILESTACK_CLOUD_STORAGE = process.env.FILESTACK_CLOUD_STORAGE;
  envVars.FilestackDefaultImagesUrl = process.env.FILESTACK_DEFAULT_IMAGES_URL;
  envVars.FILESTACK_CIPHER = process.env.FILESTACK_CIPHER;
  envVars.FILESTACK_SECRET = process.env.FILESTACK_SECRET;
  envVars.FILESTACK_DEFAULT_EXPIRY = 900;

  envVars.SECURE_AUTHENTICATED_IMAGES = process.env.SECURE_AUTHENTICATED_IMAGES;
  envVars.SECURE_AUTHENTICATED_IMAGES_KEY = process.env.SECURE_AUTHENTICATED_IMAGES_KEY;

  envVars.MYGUIDE_URL = process.env.MYGUIDE_URL;
  envVars.CORP_SITE_URL = process.env.CORP_SITE_URL;
  envVars.LXP_ENV_TYPE = process.env.LXP_ENV_TYPE;

  // read only once (version changes only on new build)
  var version = require(path.resolve('.') + '/version.json');

  // This function updates CSP
  const updateCspPolicy = async () => {
    const newCspStringValue = await getCspData(`${process.env.PLATFORM_HOST}`);
    // need to parse it in json
    const newJson = JSON.parse(newCspStringValue);
    if (newJson && typeof newJson === 'object') {
      cspPolicy = getStringifyData(newJson);
    }
  };

  if (process.env.NODE_ENV === 'production' && typeof process.env.CI === 'undefined') {
    // Get the data from API and update the CSP Policy (initial level)
    updateCspPolicy();
    // API will fetch new data after every 60 mins
    //and update the CSP without restart of the server or rotating pods
    setInterval(() => {
      updateCspPolicy();
    }, 3600000);
  }

  var renderPage = function(req, res, meta = {}) {
    res.set('X-Frame-Options', 'sameorigin');
    res.set('Cache-Control', 'no-store, no-cache, must-revalidate, private');
    let hostname = req.hostname;
    let hideConsole = true;
    if (process.env.NODE_ENV === 'development') {
      try {
        hideConsole = false;
        hostname = process.env.ENV_HOST.split('//')[1];
      } catch (e) {}
    }
    const ipAddress =
      req.headers['x-forwarded-for'] || req.socket.remoteAddress || req.connection.remoteAddress;
    getOrgInfo(
      hostname,
      function(resp) {
        let meta = {};
        if (resp) {
          // If org has a custom domain and not on that domain, redirect
          if (resp.customDomain && resp.customDomain !== req.hostname) {
            try {
              const redirectUri = new URL(req.url, `https://${resp.customDomain}`);
              return res.redirect(redirectUri.href, 301);
            } catch (e) {
              console.error('Unable to generate valid URL for customdomain redirect', e);
            }
          }
          meta = {
            image: resp.imageUrl || resp.bannerUrl || null,
            description: resp.description || null,
            title: resp.name || null
          };

          // We just need to make sure it's not empty
          if (resp.favicons && resp.favicons.tiny) {
            let sizes = {
              tiny: '16x16',
              small: '96x96',
              medium: '192x192',
              large: '32x32'
            };
            // Generate array of favicons
            meta.favicons = [];
            if (Object.keys(resp.favicons).length > 0) {
              Object.keys(sizes).forEach(function(key) {
                const link =
                  '<link rel="icon" href="' +
                  resp.favicons[key] +
                  '" type="image/x-icon" sizes="' +
                  sizes[key] +
                  '">';
                meta.favicons.push(link);
              });
            }
          }
        }

        let gtmTrackingId = null;
        let gtmTrackingEnv = null;
        try {
          const foundId = resp.configs.find(config => config.name === 'GATrackingId').value.trim();
          if (foundId && foundId.startsWith('GTM-')) {
            const gtmSplit = foundId.split('&');
            const gtmId = gtmSplit[0];
            const gtmEnv = gtmSplit.slice(1).join('&');
            gtmTrackingId = gtmId;
            gtmTrackingEnv = gtmEnv ? `&${gtmEnv}` : '';
          }
        } catch (_err) {}

        // Everything should be handled by React Router
        var newParams = Object.assign(
          {},
          viewParams,
          { meta },
          { __edOrgData: resp },
          { hideConsole },
          { cspPolicy },
          { gtmTrackingId },
          { gtmTrackingEnv }
        );

        return res.render('page.html', newParams);
      },
      ipAddress
    );
  };

  // TODO: recover New Relic!

  function getOrgInfo(hostname, callback, ipAddress) {
    if (process.env.CI === 'jenkins') {
      var ciDefaults = require('./ciOrgInfo')();
      callback(ciDefaults);
      return;
    }

    const options = {
      hostname: hostname,
      port: 443,
      path: '/api/v2/organizations/details.json',
      method: 'GET',
      headers: { 'x-lx-real-client-ip': ipAddress } // Use lx-real-ip instead of X-Forwarded-For this is for security fix LXC-5597
    };

    const req = https.request(options, res => {
      let body = '';
      res.on('data', d => {
        body += d;
      });

      res.on('end', function() {
        let resp = null;
        try {
          resp = JSON.parse(body);
        } catch (e) {}

        callback(resp);
      });
    });

    req.on('error', error => {
      console.error(error, 'SWAT-14498 ERR 134');
      callback(null);
    });

    req.end();
  }

  app.use('/log_in', function(req, res) {
    return renderPage(req, res);
  });

  const CORP_SITE_URL = `${process.env.CORP_SITE_URL || 'http://wp-corp.edcast.com/corp/'}`;
  const CORP_SITE_NAME = CORP_SITE_URL.match(/:\/\/(.[^/]+)/)[1];

  var re = new RegExp(CORP_SITE_NAME, 'g');
  if (process.env.NODE_ENV === 'production') {
    app.use('/', function(req, res, next) {
      if (req.path === '/' && req.hostname && req.hostname.split('.')[0] === 'www') {
        request.get(CORP_SITE_URL, function(err, resp) {
          var html = resp.text;
          html = html.replace(re, req.hostname);
          return res.send(html);
        });
      } else {
        return next();
      }
    });
  }

  // only static params that doesn't depend on such of process.env (because it's cached by service worker)
  const PATH = process.env.PROXY_VM ? process.env.PROXY_VM : 'localhost';
  var viewParams =
    process.env.NODE_ENV === 'production'
      ? {
          common: '/dist-' + version.v + '-common.js',
          bootstrap: '/dist-' + version.v + '-bootstrap.js',
          app: '/dist-' + version.v + '-main.js',
          css: '/dist-' + version.v + '-main.css',
          env_vars: JSON.stringify(envVars)
        }
      : {
          common: `http://${PATH}:8001/common.js`,
          bootstrap: `http://${PATH}:8001/bootstrap.js`,
          app: `http://${PATH}:8001/main.js`,
          css: `http://${PATH}:8001/main.css`,
          env_vars: JSON.stringify(envVars)
        };

  /* All page routes */
  router.get(['*'], function(req, res) {
    renderPage(req, res);
  });

  router.post('/saml/auth/:uuid', function(req, res) {
    const params = {
      SAMLRequest: req.body.SAMLRequest,
      RelayState: req.body.RelayState
    };

    const searchParams = new URLSearchParams(params);
    res.redirect(`/saml/auth/${req.params.uuid}?${searchParams.toString()}`);
  });

  app.disable('etag');

  app.use('/', router);

  app.use(function(req, res, next) {
    res.redirect('/404');
  });
};
